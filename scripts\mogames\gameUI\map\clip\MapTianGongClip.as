package mogames.gameUI.map.clip
{
   import file.MissionConfig;
   import mogames.Layers;
   import mogames.Monkey;
   import mogames.event.UIEvent;
   import mogames.gameData.base.Sint;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseMissionVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameUI.map.co.MapTianGongPanel;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameUI.tip.TipManager;
   
   public class MapTianGongClip extends BaseMap
   {
      private var _map:MapTianGongPanel;
      
      public function MapTianGongClip()
      {
         super();
      }
      
      override protected function LayoutMap() : void
      {
         this._map = new MapTianGongPanel();
         Layers.btmLayer.addChild(this._map);
         this.initStatus();
         this._map.addEventListener(UIEvent.MAP_EVENT,this.onMap,false,0,true);
         EffectManager.instance().playBGM("BGM_TIAN_GONG");
         
         // 检查天宫特色功能开放状态
         this.checkTianGongFeatures();
      }
      
      private function initStatus() : void
      {
         var _loc1_:Object = null;
         var _loc2_:BaseMissionVO = null;
         for each(_loc1_ in this._map.missions)
         {
            _loc2_ = MissionConfig.instance().findMissionVO(_loc1_.mID.v);
            if(_loc2_)
            {
               if(_loc2_.isComplete())
               {
                  _loc1_.mc.gotoAndStop("complete");
               }
               else if(_loc2_.isOpen())
               {
                  _loc1_.mc.gotoAndStop("open");
               }
               else
               {
                  _loc1_.mc.gotoAndStop("close");
               }
            }
         }
      }
      
      private function checkTianGongFeatures() : void
      {
         // 检查天兵营寨是否开放 (假设需要完成南天门任务)
         this._map.showTipTianBing(FlagProxy.instance().isComplete(3001));
         
         // 检查蟠桃园是否开放 (假设需要完成凌霄宝殿任务)
         this._map.showTipPanTao(FlagProxy.instance().isComplete(3101));
      }
      
      private function onMap(param1:UIEvent) : void
      {
         switch(param1.data.type)
         {
            case "onMissionTip":
               this.showTip(param1.data.data);
               break;
            case "onMission":
               this.onEnterMission(param1.data.data);
               break;
            case "btnNanTianMen":
               this.handlerNanTianMen();
               break;
            case "btnLingXiaoBaoDian":
               this.handlerLingXiaoBaoDian();
               break;
            case "btnPanTaoYuan":
               this.handlerPanTaoYuan();
               break;
            case "btnTianBing":
               this.handlerTianBing();
               break;
         }
      }
      
      private function showTip(param1:Sint) : void
      {
         var _loc2_:BaseMissionVO = MissionConfig.instance().findMissionVO(param1.v);
         if(!_loc2_.infor)
         {
            return;
         }
         TipManager.instance().showTip(_loc2_.infor,5000);
      }
      
      private function onEnterMission(param1:Sint) : void
      {
         var _loc2_:BaseMissionVO = MissionConfig.instance().findMissionVO(param1.v);
         if(!_loc2_)
         {
            return;
         }
         if(!_loc2_.isOpen())
         {
            MiniMsgMediator.instance().showAutoMsg("条件不符，无法进入！");
            return;
         }
         this.gotoMission(_loc2_.missionID.v);
      }
      
      private function handlerNanTianMen() : void
      {
         // 南天门特殊处理逻辑
         if(!FlagProxy.instance().isComplete(2999) && !Monkey.isLocal)
         {
            MiniMsgMediator.instance().showAutoMsg("需要先完成妖岛所有任务才能进入天宫！");
            return;
         }
         this.onEnterMission(new Sint(3000));
      }
      
      private function handlerLingXiaoBaoDian() : void
      {
         // 凌霄宝殿特殊处理逻辑
         if(!FlagProxy.instance().isComplete(3001) && !Monkey.isLocal)
         {
            MiniMsgMediator.instance().showAutoMsg("通关【南天门】后方可进入凌霄宝殿！");
            return;
         }
         this.onEnterMission(new Sint(3100));
      }
      
      private function handlerPanTaoYuan() : void
      {
         // 蟠桃园特殊处理逻辑
         if(!FlagProxy.instance().isComplete(3101) && !Monkey.isLocal)
         {
            MiniMsgMediator.instance().showAutoMsg("通关【凌霄宝殿】后方可进入蟠桃园！");
            return;
         }
         this.onEnterMission(new Sint(3200));
      }
      
      private function handlerTianBing() : void
      {
         // 天兵营寨特殊处理逻辑
         if(!FlagProxy.instance().isComplete(3001) && !Monkey.isLocal)
         {
            MiniMsgMediator.instance().showAutoMsg("通关【南天门】后方可进入天兵营寨！");
            return;
         }
         this.onEnterMission(new Sint(3300));
      }
      
      override public function destroy() : void
      {
         if(this._map)
         {
            this._map.removeEventListener(UIEvent.MAP_EVENT,this.onMap);
            this._map.destroy();
            this._map = null;
         }
      }
   }
}
