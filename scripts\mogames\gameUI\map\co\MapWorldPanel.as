package mogames.gameUI.map.co
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import mogames.AssetManager;
   import mogames.event.UIEvent;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameObj.display.BaseSprite;
   import utils.MethodUtil;
   
   public class MapWorldPanel extends BaseSprite
   {
      private var ui:MovieClip;
      
      private var _pos:Array = [[527,244],[240,355],[352,182],[746,91]];
      
      public function MapWorldPanel()
      {
         super();
         this.ui = AssetManager.newMCRes("UI_AREA_WORLD_MAP");
         addChild(this.ui);
         this.ui.btnDongTu.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.ui.btnTuBo.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.ui.btnXiYu.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.ui.btnTianZhu.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.ui.btnLongGong.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.ui.btnYaoDao.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.ui.btnTianGong.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.ui.btnTower.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.ui.btnFB.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.ui.btnQS.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.ui.mcArrow.visible = false;
         this.ui.mcArrow.stop();
         MethodUtil.enableBtn(this.ui.btnTianGong,FlagProxy.instance().isComplete(1040));
         this.initArea();
         this.updateHSTip();
      }
      
      public function updateHSTip() : void
      {
         var _loc1_:Boolean = FlagProxy.instance().isComplete(216);
         this.ui.mcHSTip.visible = !_loc1_;
         if(!_loc1_)
         {
            this.ui.mcHSTip.play();
         }
         else
         {
            this.ui.mcHSTip.stop();
         }
      }
      
      private function initArea() : void
      {
         if(Monkey.isLocal)
         {
            return;
         }
         MethodUtil.enableBtn(this.ui.btnTuBo,FlagProxy.instance().isComplete(1009));
         MethodUtil.enableBtn(this.ui.btnXiYu,FlagProxy.instance().isComplete(1018));
         MethodUtil.enableBtn(this.ui.btnTianZhu,FlagProxy.instance().isComplete(1026));
         MethodUtil.gray(this.ui.btnYaoDao,!FlagProxy.instance().isComplete(1033));
         MethodUtil.gray(this.ui.btnLongGong,!FlagProxy.instance().isComplete(1009));
         MethodUtil.gray(this.ui.btnTianGong,!FlagProxy.instance().isComplete(1040));
         if(FlagProxy.instance().isComplete(1040))
         {
            this.showArrow(4);
         }
         else if(FlagProxy.instance().isComplete(1033))
         {
            this.showArrow(3);
         }
         else if(FlagProxy.instance().isComplete(1026))
         {
            this.showArrow(2);
         }
         else if(FlagProxy.instance().isComplete(1018))
         {
            this.showArrow(1);
         }
         else if(FlagProxy.instance().isComplete(1009))
         {
            this.showArrow(0);
         }
      }
      
      public function showArrow(param1:int) : void
      {
         this.ui.mcArrow.visible = true;
         this.ui.mcArrow.play();
         this.ui.mcArrow.x = this._pos[param1][0];
         this.ui.mcArrow.y = this._pos[param1][1];
      }
      
      private function onDown(param1:MouseEvent) : void
      {
         dispatchEvent(new UIEvent(UIEvent.MAP_EVENT,{"type":param1.target.name}));
      }
      
      override public function destroy() : void
      {
         this.ui.btnDongTu.removeEventListener(MouseEvent.CLICK,this.onDown);
         this.ui.btnTuBo.removeEventListener(MouseEvent.CLICK,this.onDown);
         this.ui.btnXiYu.removeEventListener(MouseEvent.CLICK,this.onDown);
         this.ui.btnTianZhu.removeEventListener(MouseEvent.CLICK,this.onDown);
         this.ui.btnLongGong.removeEventListener(MouseEvent.CLICK,this.onDown);
         this.ui.btnYaoDao.removeEventListener(MouseEvent.CLICK,this.onDown);
         this.ui.btnTianGong.removeEventListener(MouseEvent.CLICK,this.onDown);
         this.ui.btnTower.removeEventListener(MouseEvent.CLICK,this.onDown);
         this.ui.btnFB.removeEventListener(MouseEvent.CLICK,this.onDown);
         this.ui.btnQS.removeEventListener(MouseEvent.CLICK,this.onDown);
         super.destroy();
      }
   }
}

