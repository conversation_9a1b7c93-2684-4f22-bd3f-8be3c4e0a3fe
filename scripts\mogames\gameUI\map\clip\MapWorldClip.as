package mogames.gameUI.map.clip
{
   import mogames.Layers;
   import mogames.event.UIEvent;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameUI.fuben.TZQS.TZQSEnterModule;
   import mogames.gameUI.fuben.daily.DailyFBModule;
   import mogames.gameUI.fuben.tower.TowerModule;
   import mogames.gameUI.map.AreaManager;
   import mogames.gameUI.map.co.MapWorldPanel;
   import mogames.gameUI.prompt.MiniMsgMediator;
   
   public class MapWorldClip extends BaseMap
   {
      private var _map:MapWorldPanel;
      
      public function MapWorldClip()
      {
         super();
      }
      
      override protected function LayoutMap() : void
      {
         this._map = new MapWorldPanel();
         Layers.btmLayer.addChild(this._map);
         this._map.addEventListener(UIEvent.MAP_EVENT,this.onMap,false,0,true);
         EffectManager.instance().playBGM("BGM_WORLD");
      }
      
      private function onMap(param1:UIEvent) : void
      {
         switch(param1.data.type)
         {
            case "btnDongTu":
               AreaManager.instance().changeMap(1);
               break;
            case "btnTuBo":
               AreaManager.instance().changeMap(2);
               break;
            case "btnXiYu":
               AreaManager.instance().changeMap(3);
               break;
            case "btnTianZhu":
               AreaManager.instance().changeMap(4);
               break;
            case "btnYaoDao":
               this.handlerYaoDao();
               break;
            case "btnLongGong":
               AreaManager.instance().changeMap(200);
               break;
            case "btnTianGong":
               this.handlerTianGong();
               break;
            case "btnTower":
               TowerModule.instance().init();
               break;
            case "btnFB":
               DailyFBModule.instance().init();
               EffectManager.instance().playAudio("CLICK");
               break;
            case "btnQS":
               TZQSEnterModule.instance().init();
         }
      }
      
      private function handlerYaoDao() : void
      {
         if(!FlagProxy.instance().isComplete(1033) && !Monkey.isLocal)
         {
            MiniMsgMediator.instance().showAutoMsg("通关天竺地图后方可进入妖岛！");
            return;
         }
         AreaManager.instance().changeMap(5);
      }

      private function handlerTianGong() : void
      {
         if(!FlagProxy.instance().isComplete(1040) && !Monkey.isLocal)
         {
            MiniMsgMediator.instance().showAutoMsg("通关妖岛所有任务后方可进入天宫！");
            return;
         }
         AreaManager.instance().changeMap(300);
      }
      
      override public function destroy() : void
      {
         this._map.destroy();
         this._map = null;
      }
   }
}

