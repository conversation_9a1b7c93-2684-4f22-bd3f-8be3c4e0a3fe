package file
{
   import flash.geom.Point;
   import flash.utils.Dictionary;
   import mogames.gameData.mission.BaseMissionVO;
   import mogames.gameData.mission.BaseSceneVO;
   
   public class MissionConfig
   {
      private static var _instance:MissionConfig;
      
      public var missionDic:Dictionary;
      
      private var _missionVec:Vector.<BaseMissionVO>;
      
      private var _sceneVec:Vector.<BaseSceneVO>;
      
      public function MissionConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : MissionConfig
      {
         if(!_instance)
         {
            _instance = new MissionConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this.missionDic = new Dictionary();
         this.missionDic["xinshou"] = [];
         this.missionDic["xinshou"].push("enemy/enemyTianBing.swf");
         this.missionDic["xinshou"].push("animation/comicStart.swf");
         this.missionDic["xinshou"].push("scene/sceneXuZhang1.swf");
         this.missionDic["xinshou"].push("scene/sceneXuZhang2.swf");
         this.missionDic["xinshou"].push("enemy/bossFoShou.swf");
         this.missionDic["huaguoshan"] = [];
         this.missionDic["huaguoshan"].push("scene/sceneHuaGuoShan-340.swf");
         this.missionDic["huaguoshan"].push("scene/sceneShuiLianDong.swf");
         this.missionDic["huaguoshan"].push("enemy/enemyYouJinLing.swf");
         this.missionDic["huaguoshan"].push("enemy/enemyShuJinLing.swf");
         this.missionDic["wuzhuangguan"] = [];
         this.missionDic["wuzhuangguan"].push("scene/sceneWuZhuangGuan-310.swf");
         this.missionDic["wuzhuangguan"].push("scene/herbs-450.swf");
         this.missionDic["wuzhuangguan"].push("enemy/enemyHuGuai.swf");
         this.missionDic["wuzhuangguan"].push("enemy/enemyXiongGuai.swf");
         this.missionDic["wuzhuangguan"].push("enemy/enemyMoYan.swf");
         this.missionDic["jingjiling"] = [];
         this.missionDic["jingjiling"].push("scene/sceneJingJiLing-390.swf");
         this.missionDic["jingjiling"].push("scene/sceneZhuLin-340.swf");
         this.missionDic["jingjiling"].push("enemy/enemySheLing.swf");
         this.missionDic["jingjiling"].push("enemy/enemyNiuXiaoGuai.swf");
         this.missionDic["longgong"] = [];
         this.missionDic["longgong"].push("scene/sceneLongGong-720.swf");
         this.missionDic["longgong"].push("enemy/enemyXiaBingDa.swf");
         this.missionDic["longgong"].push("enemy/enemyXieJiangDa.swf");
         this.missionDic["longgong"].push("enemy/enemyShuiLin.swf");
         this.missionDic["daxiongbaodian"] = [];
         this.missionDic["daxiongbaodian"].push("scene/sceneDXBD.swf");
         this.missionDic["TOWER00"] = [];
         this.missionDic["TOWER00"].push("scene/sceneZhenYaoTa-170.swf");
         this.missionDic["TOWER00"].push("enemy/enemyHuGuai.swf");
         this.missionDic["TOWER00"].push("enemy/enemyBianFuGuai.swf");
         this.missionDic["TOWER00"].push("enemy/bossYinJiangJun.swf");
         this.missionDic["TOWER00"].push("enemy/enemyXiongGuai.swf");
         this.missionDic["TOWER00"].push("enemy/bossHeiXiongJing.swf");
         this.missionDic["TOWER00"].push("enemy/enemyHuFaShi.swf");
         this.missionDic["TOWER00"].push("enemy/bossHuangFengGuai.swf");
         this.missionDic["TOWER00"].push("enemy/bossHuangPaoGuai.swf");
         this.missionDic["TOWER00"].push("enemy/enemyJinJiaoGuai.swf");
         this.missionDic["TOWER00"].push("enemy/enemyYinJiaoGuai.swf");
         this.missionDic["TOWER00"].push("enemy/bossYinJiaoDaWang.swf");
         this.missionDic["TOWER00"].push("enemy/bossJinJiaoDaWang.swf");
         this.missionDic["TOWER00"].push("enemy/enemyYuHu.swf");
         this.missionDic["TOWER00"].push("enemy/enemyKuLouGongBing.swf");
         this.missionDic["TOWER00"].push("enemy/bossShiLiWang.swf");
         this.missionDic["TOWER00"].push("enemy/bossZhenShiLiWang.swf");
         this.missionDic["TOWER01"] = [];
         this.missionDic["TOWER01"].push("scene/sceneZhenYaoTa-170.swf");
         this.missionDic["TOWER01"].push("enemy/enemyHuGuai.swf");
         this.missionDic["TOWER01"].push("enemy/enemyXiaBing.swf");
         this.missionDic["TOWER01"].push("enemy/enemyXieJiang.swf");
         this.missionDic["TOWER01"].push("enemy/enemyHuFaShi.swf");
         this.missionDic["TOWER01"].push("enemy/enemyKuLouGongBing.swf");
         this.missionDic["TOWER01"].push("enemy/enemyYuHu.swf");
         this.missionDic["TOWER01"].push("enemy/enemyXiaoYaoOne-140.swf");
         this.missionDic["TOWER01"].push("enemy/enemyXiaoYaoTwo-140.swf");
         this.missionDic["TOWER01"].push("enemy/bossChanChu-130.swf");
         this.missionDic["TOWER01"].push("enemy/bossBangJing-170.swf");
         this.missionDic["TOWER01"].push("enemy/bossDengYuGuai-130.swf");
         this.missionDic["TOWER01"].push("enemy/bossJiJuXie.swf");
         this.missionDic["TOWER01"].push("enemy/bossHongHaiEr-210.swf");
         this.missionDic["TOWER01"].push("enemy/bossTuoLong.swf");
         this.missionDic["TOWER01"].push("enemy/bossZhenTuoLong-170.swf");
         this.missionDic["TOWER01"].push("enemy/bossHuDaXian-170.swf");
         this.missionDic["TOWER02"] = [];
         this.missionDic["TOWER02"].push("scene/sceneZhenYaoTa-170.swf");
         this.missionDic["TOWER02"].push("enemy/enemyHuYao-170.swf");
         this.missionDic["TOWER02"].push("enemy/enemyYangYao-170.swf");
         this.missionDic["TOWER02"].push("enemy/enemyLuYao-170.swf");
         this.missionDic["TOWER02"].push("enemy/enemyNiuXiaoGuai.swf");
         this.missionDic["TOWER02"].push("enemy/enemyNiuLaoGuai.swf");
         this.missionDic["TOWER02"].push("enemy/enemyXiaBingDa.swf");
         this.missionDic["TOWER02"].push("enemy/enemyXieJiangDa.swf");
         this.missionDic["TOWER02"].push("enemy/bossYangDaXian-180.swf");
         this.missionDic["TOWER02"].push("enemy/bossLuDaXian-180.swf");
         this.missionDic["TOWER02"].push("enemy/bossLingGanWang.swf");
         this.missionDic["TOWER02"].push("enemy/bossDuJiao.swf");
         this.missionDic["TOWER02"].push("enemy/bossZhenDuJiao.swf");
         this.missionDic["TOWER02"].push("enemy/bossXieZi.swf");
         this.missionDic["TOWER02"].push("enemy/bossZhenXieZi.swf");
         this.missionDic["TOWER02"].push("enemy/bossNiBaGuai.swf");
         this.missionDic["TOWER10"] = [];
         this.missionDic["TOWER10"].push("scene/sceneZhenYaoTa-170.swf");
         this.missionDic["TOWER10"].push("enemy/enemyNiuXiaoGuai.swf");
         this.missionDic["TOWER10"].push("enemy/enemyYangYao-170.swf");
         this.missionDic["TOWER10"].push("enemy/enemyYuHu.swf");
         this.missionDic["TOWER10"].push("enemy/enemyNiuLaoGuai.swf");
         this.missionDic["TOWER10"].push("enemy/bossYanShuJing.swf");
         this.missionDic["TOWER10"].push("enemy/bossJuYanJing.swf");
         this.missionDic["TOWER10"].push("enemy/bossYanShiJiang.swf");
         this.missionDic["TOWER10"].push("enemy/bossTieShan.swf");
         this.missionDic["TOWER10"].push("enemy/bossNiuMoWang.swf");
         this.missionDic["TOWER10"].push("enemy/bossJiuTouChong.swf");
         this.missionDic["TOWER11"] = [];
         this.missionDic["TOWER11"].push("scene/sceneZhenYaoTa-170.swf");
         this.missionDic["TOWER11"].push("enemy/enemyXieWuSeng.swf");
         this.missionDic["TOWER11"].push("enemy/enemyXieFaSeng.swf");
         this.missionDic["TOWER11"].push("enemy/enemyGreenSnake.swf");
         this.missionDic["TOWER11"].push("enemy/enemyRedSnake.swf");
         this.missionDic["TOWER11"].push("enemy/enemyJiaMianGuai.swf");
         this.missionDic["TOWER11"].push("enemy/bossHuangMei.swf");
         this.missionDic["TOWER11"].push("enemy/bossMangSheJing-530.swf");
         this.missionDic["TOWER11"].push("enemy/bossJinMaoHou.swf");
         this.missionDic["TOWER11"].push("enemy/bossZhiZhuJing.swf");
         this.missionDic["TOWER11"].push("enemy/bossWuGongJing.swf");
         this.missionDic["TOWER11"].push("enemy/bossBaiLuJing.swf");
         this.missionDic["TOWER11"].push("enemy/bossDiYong.swf");
         this.missionDic["TOWER12"] = [];
         this.missionDic["TOWER12"].push("scene/sceneZhenYaoTa-170.swf");
         this.missionDic["TOWER12"].push("enemy/enemyZiZhiZhu.swf");
         this.missionDic["TOWER12"].push("enemy/enemyLvZhiZhu.swf");
         this.missionDic["TOWER12"].push("enemy/enemyHuangWuGong.swf");
         this.missionDic["TOWER12"].push("enemy/enemyLanWuGong.swf");
         this.missionDic["TOWER12"].push("enemy/enemyBaiXiangYao.swf");
         this.missionDic["TOWER12"].push("enemy/bossBaiXiangJing.swf");
         this.missionDic["TOWER12"].push("enemy/bossShiZiJing.swf");
         this.missionDic["TOWER12"].push("enemy/bossBaoZiJing.swf");
         this.missionDic["TOWER12"].push("enemy/bossHuangShiJing.swf");
         this.missionDic["TOWER12"].push("enemy/bossYuanSheng.swf");
         this.missionDic["TOWER12"].push("enemy/bossZhenYuanSheng.swf");
         this.missionDic["shilian"] = [];
         this.missionDic["shilian"].push("scene/sceneShiLian-562.swf");
         this.missionDic["shilian"].push("enemy/enemyHuGuai.swf");
         this.missionDic["cangbaodong"] = [];
         this.missionDic["cangbaodong"].push("scene/sceneCangBaoDong.swf");
         this.missionDic["cangbaodong"].push("enemy/enemyBaoXiangGuai.swf");
         this.missionDic["jingjichang"] = [];
         this.missionDic["jingjichang"].push("scene/sceneJingJiChang-350.swf");
         this.missionDic["huangquanlu"] = [];
         this.missionDic["huangquanlu"].push("scene/sceneHuangQuanLu.swf");
         this.missionDic["huangquanlu"].push("enemy/enemyNiuTou.swf");
         this.missionDic["huangquanlu"].push("enemy/enemyMaMian.swf");
         this.missionDic["saitaisui"] = [];
         this.missionDic["saitaisui"].push("scene/sceneSaiTaiSui.swf");
         this.missionDic["saitaisui"].push("enemy/bossSaiTaiSui.swf");
         this.missionDic["shuiqilin"] = [];
         this.missionDic["shuiqilin"].push("scene/sceneShuiQiLin.swf");
         this.missionDic["shuiqilin"].push("enemy/bossShuiQiLin.swf");
         this.missionDic["huofenghuang"] = [];
         this.missionDic["huofenghuang"].push("scene/sceneShuiQiLin.swf");
         this.missionDic["huofenghuang"].push("enemy/bossFengHuang.swf");
         this.missionDic["xuanwu"] = [];
         this.missionDic["xuanwu"].push("scene/sceneXuanWu.swf");
         this.missionDic["xuanwu"].push("enemy/bossXuanWu.swf");
         this.missionDic["qisheng"] = [];
         this.missionDic["qisheng"].push("scene/sceneQiSheng.swf");
         this.missionDic["qisheng"].push("enemy/qsNiuMoWang.swf");
         this.missionDic["qisheng"].push("enemy/qsJiaoMoWang.swf");
         this.missionDic["qisheng"].push("enemy/qsPengMoWang.swf");
         this.missionDic["qisheng"].push("enemy/qsShiTuoWang.swf");
         this.missionDic["qisheng"].push("enemy/qsMiHouWang.swf");
         this.missionDic["qisheng"].push("enemy/qsYuRongWang.swf");
         this.missionDic["qisheng"].push("enemy/qsMeiHouWang.swf");
         this.missionDic["migong"] = [];
         this.missionDic["migong"].push("scene/sceneMiGong.swf");
         this.missionDic["migong"].push("enemy/enemyXiaBing.swf");
         this.missionDic["migong"].push("enemy/enemyXieJiang.swf");
         this.missionDic["migong"].push("enemy/enemyXiaBingDa.swf");
         this.missionDic["migong"].push("enemy/enemyXieJiangDa.swf");
         this.missionDic["migong"].push("enemy/bossBangJing-170.swf");
         this.missionDic["migong"].push("enemy/bossDengYuGuai-130.swf");
         this.missionDic["migong"].push("enemy/bossJiJuXie.swf");
         this.missionDic["migong"].push("enemy/bossTuoLong.swf");
         this.missionDic["migong"].push("enemy/bossZhenTuoLong-170.swf");
         this.missionDic["zaixiangfu"] = [];
         this.missionDic["zaixiangfu"].push("scene/sceneZaiXiangFu.swf");
         this.missionDic["zaixiangfu"].push("enemy/bossGuiChengXiang.swf");
         this.missionDic["shanhujiao"] = [];
         this.missionDic["shanhujiao"].push("scene/sceneShanHuJiao.swf");
         this.missionDic["shanhujiao"].push("enemy/bossHuJiao.swf");
         this.missionDic["chenchuan"] = [];
         this.missionDic["chenchuan"].push("scene/sceneChenChuan.swf");
         this.missionDic["chenchuan"].push("enemy/bossRanYiYu.swf");
         this.missionDic["shenyuan"] = [];
         this.missionDic["shenyuan"].push("scene/sceneShenYuan.swf");
         this.missionDic["shenyuan"].push("enemy/bossYingYu.swf");
         this.missionDic["union"] = [];
         this.missionDic["union"].push("scene/sceneUnionBattle.swf");
         this.missionDic["union"].push("enemy/bossTaoTie.swf");
         this.missionDic["union"].push("enemy/bossQiongQi.swf");
         this.missionDic["mission1"] = [];
         this.missionDic["mission1"].push("scene/scene101-120.swf");
         this.missionDic["mission1"].push("enemy/enemyHuGuai.swf");
         this.missionDic["mission1"].push("enemy/enemyBianFuGuai.swf");
         this.missionDic["mission1"].push("enemy/bossYinJiangJun.swf");
         this.missionDic["mission2"] = [];
         this.missionDic["mission2"].push("scene/scene201.swf");
         this.missionDic["mission2"].push("enemy/bossXiaoBaiLong.swf");
         this.missionDic["mission3"] = [];
         this.missionDic["mission3"].push("scene/scene301-120.swf");
         this.missionDic["mission3"].push("scene/scene302.swf");
         this.missionDic["mission3"].push("enemy/enemyBianFuGuai.swf");
         this.missionDic["mission3"].push("enemy/enemyXiongGuai.swf");
         this.missionDic["mission3"].push("enemy/bossHeiXiongJing.swf");
         this.missionDic["mission4"] = [];
         this.missionDic["mission4"].push("scene/scene401-140.swf");
         this.missionDic["mission4"].push("enemy/bossZhuBaJie-140.swf");
         this.missionDic["mission5"] = [];
         this.missionDic["mission5"].push("scene/scene501.swf");
         this.missionDic["mission5"].push("scene/scene502.swf");
         this.missionDic["mission5"].push("scene/scene503.swf");
         this.missionDic["mission5"].push("enemy/enemyHuGuai.swf");
         this.missionDic["mission5"].push("enemy/enemyBianFuGuai.swf");
         this.missionDic["mission5"].push("enemy/enemyHuFaShi.swf");
         this.missionDic["mission5"].push("enemy/bossHuangFengGuai.swf");
         this.missionDic["mission6"] = [];
         this.missionDic["mission6"].push("scene/scene601.swf");
         this.missionDic["mission6"].push("enemy/bossShaSeng.swf");
         this.missionDic["mission7"] = [];
         this.missionDic["mission7"].push("scene/scene701.swf");
         this.missionDic["mission7"].push("scene/scene70234-110.swf");
         this.missionDic["mission7"].push("scene/scene705.swf");
         this.missionDic["mission7"].push("enemy/enemyKuLouFaShi.swf");
         this.missionDic["mission7"].push("enemy/enemyKuLouBuBing.swf");
         this.missionDic["mission7"].push("enemy/enemyKuLouGongBing.swf");
         this.missionDic["mission7"].push("enemy/bossYinJiangJun.swf");
         this.missionDic["mission7"].push("enemy/bossBaiGuJingFoot.swf");
         this.missionDic["mission7"].push("enemy/bossBaiGuJingBody.swf");
         this.missionDic["mission7"].push("enemy/bossBaiGuJingHead.swf");
         this.missionDic["mission7"].push("enemy/bossYinJiangJun.swf");
         this.missionDic["mission7"].push("enemy/bossHeiXiongJing.swf");
         this.missionDic["mission7"].push("enemy/bossHuangFengGuai.swf");
         this.missionDic["mission8"] = [];
         this.missionDic["mission8"].push("scene/scene801.swf");
         this.missionDic["mission8"].push("scene/scene802.swf");
         this.missionDic["mission8"].push("scene/scene803-120.swf");
         this.missionDic["mission8"].push("scene/scene804-110.swf");
         this.missionDic["mission8"].push("scene/scene805678-110.swf");
         this.missionDic["mission8"].push("scene/scene809.swf");
         this.missionDic["mission8"].push("enemy/bossYinJiangJun.swf");
         this.missionDic["mission8"].push("enemy/bossHuangFengGuai.swf");
         this.missionDic["mission8"].push("enemy/bossHeiXiongJing.swf");
         this.missionDic["mission8"].push("enemy/bossHuangPaoGuai.swf");
         this.missionDic["mission8"].push("enemy/enemyHuGuai.swf");
         this.missionDic["mission8"].push("enemy/enemyArrowBat.swf");
         this.missionDic["mission8"].push("enemy/enemyHuFaShi.swf");
         this.missionDic["mission8"].push("enemy/enemyXiongGuai.swf");
         this.missionDic["mission9"] = [];
         this.missionDic["mission9"].push("scene/scene901-150.swf");
         this.missionDic["mission9"].push("scene/scene902-150.swf");
         this.missionDic["mission9"].push("scene/scene903-150.swf");
         this.missionDic["mission9"].push("scene/scene904-150.swf");
         this.missionDic["mission9"].push("enemy/enemyYuHu.swf");
         this.missionDic["mission9"].push("enemy/enemyArrowBat.swf");
         this.missionDic["mission9"].push("enemy/enemyJinJiaoGuai.swf");
         this.missionDic["mission9"].push("enemy/enemyYinJiaoGuai.swf");
         this.missionDic["mission9"].push("enemy/bossYinJiaoDaWang.swf");
         this.missionDic["mission9"].push("enemy/bossJinJiaoDaWang.swf");
         this.missionDic["mission9"].push("enemy/bossJiuWeiHu.swf");
         this.missionDic["mission9"].push("enemy/enemyBianFuGuai.swf");
         this.missionDic["mission10"] = [];
         this.missionDic["mission10"].push("scene/scene1001-140.swf");
         this.missionDic["mission10"].push("scene/scene1002-120.swf");
         this.missionDic["mission10"].push("scene/scene1003-130.swf");
         this.missionDic["mission10"].push("scene/sceneShuiLao-130.swf");
         this.missionDic["mission10"].push("enemy/enemyHuFaShi.swf");
         this.missionDic["mission10"].push("enemy/enemyYinJiaoGuai.swf");
         this.missionDic["mission10"].push("enemy/enemyYuHu.swf");
         this.missionDic["mission10"].push("enemy/enemyKuLouGongBing.swf");
         this.missionDic["mission10"].push("enemy/bossShiLiWang.swf");
         this.missionDic["mission10"].push("enemy/bossZhenShiLiWang.swf");
         this.missionDic["mission10"].push("enemy/bossChanChu-130.swf");
         this.missionDic["mission10"].push("enemy/bossBangJing-170.swf");
         this.missionDic["mission10"].push("enemy/bossDengYuGuai-130.swf");
         this.missionDic["mission10"].push("enemy/bossJiJuXie.swf");
         this.missionDic["mission11"] = [];
         this.missionDic["mission11"].push("scene/scene1101-140.swf");
         this.missionDic["mission11"].push("scene/scene1102-140.swf");
         this.missionDic["mission11"].push("scene/scene1103-140.swf");
         this.missionDic["mission11"].push("enemy/enemyHuFaShi.swf");
         this.missionDic["mission11"].push("enemy/enemyJinJiaoGuai.swf");
         this.missionDic["mission11"].push("enemy/enemyYuHu.swf");
         this.missionDic["mission11"].push("enemy/enemyKuLouFaShi.swf");
         this.missionDic["mission11"].push("enemy/enemyBianFuGuai.swf");
         this.missionDic["mission11"].push("enemy/enemyXiaoYaoOne-140.swf");
         this.missionDic["mission11"].push("enemy/enemyXiaoYaoTwo-140.swf");
         this.missionDic["mission11"].push("enemy/enemyKuLouBuBing.swf");
         this.missionDic["mission11"].push("enemy/enemyKuLouGongBing.swf");
         this.missionDic["mission11"].push("enemy/bossHongHaiEr-210.swf");
         this.missionDic["mission12"] = [];
         this.missionDic["mission12"].push("scene/scene1201-150.swf");
         this.missionDic["mission12"].push("scene/scene1202-150.swf");
         this.missionDic["mission12"].push("enemy/enemyXiaBing.swf");
         this.missionDic["mission12"].push("enemy/enemyXieJiang.swf");
         this.missionDic["mission12"].push("enemy/bossTuoLong.swf");
         this.missionDic["mission12"].push("enemy/bossZhenTuoLong-170.swf");
         this.missionDic["mission13"] = [];
         this.missionDic["mission13"].push("scene/scene1301-170.swf");
         this.missionDic["mission13"].push("enemy/enemyHuYao-170.swf");
         this.missionDic["mission13"].push("enemy/enemyYangYao-170.swf");
         this.missionDic["mission13"].push("enemy/enemyLuYao-170.swf");
         this.missionDic["mission13"].push("enemy/bossHuDaXian-170.swf");
         this.missionDic["mission13"].push("enemy/bossYangDaXian-180.swf");
         this.missionDic["mission13"].push("enemy/bossLuDaXian-180.swf");
         this.missionDic["mission13"].push("enemy/bossSanYaoHeTi-170.swf");
         this.missionDic["mission14"] = [];
         this.missionDic["mission14"].push("scene/scene1401.swf");
         this.missionDic["mission14"].push("enemy/enemyXiaBingDa.swf");
         this.missionDic["mission14"].push("enemy/enemyXieJiangDa.swf");
         this.missionDic["mission14"].push("enemy/bossLingGanWang.swf");
         this.missionDic["mission15"] = [];
         this.missionDic["mission15"].push("scene/scene1501.swf");
         this.missionDic["mission15"].push("enemy/enemyNiuXiaoGuai.swf");
         this.missionDic["mission15"].push("enemy/enemyNiuLaoGuai.swf");
         this.missionDic["mission15"].push("enemy/bossDuJiao.swf");
         this.missionDic["mission15"].push("enemy/bossZhenDuJiao.swf");
         this.missionDic["mission16"] = [];
         this.missionDic["mission16"].push("scene/scene1601.swf");
         this.missionDic["mission16"].push("scene/sceneZhenBei.swf");
         this.missionDic["mission16"].push("enemy/enemyKuLouFaShi.swf");
         this.missionDic["mission16"].push("enemy/enemyKuLouBuBing.swf");
         this.missionDic["mission16"].push("enemy/enemyKuLouGongBing.swf");
         this.missionDic["mission16"].push("enemy/enemyArrowBat.swf");
         this.missionDic["mission16"].push("enemy/bossXieZi.swf");
         this.missionDic["mission16"].push("enemy/bossZhenXieZi.swf");
         this.missionDic["mission16"].push("enemy/bossNiBaGuai.swf");
         this.missionDic["mission16"].push("enemy/bossYanShuJing.swf");
         this.missionDic["mission16"].push("enemy/bossJuYanJing.swf");
         this.missionDic["mission16"].push("enemy/bossYanShiJiang.swf");
         this.missionDic["mission17"] = [];
         this.missionDic["mission17"].push("scene/scene1701.swf");
         this.missionDic["mission17"].push("scene/scene1702.swf");
         this.missionDic["mission17"].push("scene/scene1703.swf");
         this.missionDic["mission17"].push("scene/scene1704.swf");
         this.missionDic["mission17"].push("scene/scene1705.swf");
         this.missionDic["mission17"].push("enemy/enemyHuFaShi.swf");
         this.missionDic["mission17"].push("enemy/enemyXiaoYaoTwo-140.swf");
         this.missionDic["mission17"].push("enemy/enemyYuHu.swf");
         this.missionDic["mission17"].push("enemy/enemyNiuLaoGuai.swf");
         this.missionDic["mission17"].push("enemy/bossTieShan.swf");
         this.missionDic["mission17"].push("enemy/bossNiuMoWang.swf");
         this.missionDic["mission17"].push("enemy/bossZhenNiuMo.swf");
         this.missionDic["mission18"] = [];
         this.missionDic["mission18"].push("scene/scene1801.swf");
         this.missionDic["mission18"].push("enemy/enemyNiuXiaoGuai.swf");
         this.missionDic["mission18"].push("enemy/enemyYangYao-170.swf");
         this.missionDic["mission18"].push("enemy/enemyYuHu.swf");
         this.missionDic["mission18"].push("enemy/bossJiuTouChong.swf");
         this.missionDic["mission18"].push("enemy/bossZhenJiuTou.swf");
         this.missionDic["mission19"] = [];
         this.missionDic["mission19"].push("scene/scene1901.swf");
         this.missionDic["mission19"].push("enemy/enemyXieWuSeng.swf");
         this.missionDic["mission19"].push("enemy/enemyXieFaSeng.swf");
         this.missionDic["mission19"].push("enemy/bossHuangMei.swf");
         this.missionDic["mission20"] = [];
         this.missionDic["mission20"].push("scene/scene2001.swf");
         this.missionDic["mission20"].push("enemy/enemyGreenSnake.swf");
         this.missionDic["mission20"].push("enemy/enemyRedSnake.swf");
         this.missionDic["mission20"].push("enemy/bossMangSheJing-530.swf");
         this.missionDic["mission21"] = [];
         this.missionDic["mission21"].push("scene/sceneQiLinShan.swf");
         this.missionDic["mission21"].push("enemy/enemyJiaMianGuai.swf");
         this.missionDic["mission21"].push("enemy/enemyBianFuGuai.swf");
         this.missionDic["mission21"].push("enemy/enemyArrowBat.swf");
         this.missionDic["mission21"].push("enemy/bossJinMaoHou.swf");
         this.missionDic["mission22"] = [];
         this.missionDic["mission22"].push("scene/scene2201.swf");
         this.missionDic["mission22"].push("scene/mcWhiteSpider.swf");
         this.missionDic["mission22"].push("scene/mcRedSpider.swf");
         this.missionDic["mission22"].push("scene/mcPurpleSpider.swf");
         this.missionDic["mission22"].push("scene/mcSpiderEgg.swf");
         this.missionDic["mission22"].push("enemy/enemyZiZhiZhu.swf");
         this.missionDic["mission22"].push("enemy/enemyLvZhiZhu.swf");
         this.missionDic["mission22"].push("enemy/bossZhiZhuJing.swf");
         this.missionDic["mission23"] = [];
         this.missionDic["mission23"].push("scene/sceneHuangHuaGuan.swf");
         this.missionDic["mission23"].push("enemy/enemyHuangWuGong.swf");
         this.missionDic["mission23"].push("enemy/enemyLanWuGong.swf");
         this.missionDic["mission23"].push("enemy/bossZhiZhuJing.swf");
         this.missionDic["mission23"].push("enemy/bossWuGongJing.swf");
         this.missionDic["mission24"] = [];
         this.missionDic["mission24"].push("scene/sceneShiTuoLing.swf");
         this.missionDic["mission24"].push("enemy/bossBaiXiangJing.swf");
         this.missionDic["mission24"].push("enemy/bossShiZiJing.swf");
         this.missionDic["mission24"].push("enemy/bossDaPengJing.swf");
         this.missionDic["mission24"].push("enemy/enemyBaiXiangYao.swf");
         this.missionDic["mission24"].push("enemy/enemyJiaMianGuai.swf");
         this.missionDic["mission25"] = [];
         this.missionDic["mission25"].push("scene/sceneQingHuaDong.swf");
         this.missionDic["mission25"].push("enemy/bossBaiLuJing.swf");
         this.missionDic["mission25"].push("enemy/enemyShiXiaoYao.swf");
         this.missionDic["mission25"].push("enemy/enemyBaiMianFox.swf");
         this.missionDic["mission26"] = [];
         this.missionDic["mission26"].push("scene/sceneXianKongShan.swf");
         this.missionDic["mission26"].push("enemy/bossDiYong.swf");
         this.missionDic["mission26"].push("enemy/enemyPengXiaoYao-650.swf");
         this.missionDic["mission26"].push("enemy/enemyXieWuSeng.swf");
         this.missionDic["mission26"].push("enemy/enemyBaiXiangYao.swf");
         this.missionDic["mission27"] = [];
         this.missionDic["mission27"].push("scene/sceneYinWuShan-550.swf");
         this.missionDic["mission27"].push("enemy/enemyJiaMianGuai.swf");
         this.missionDic["mission27"].push("enemy/enemyCangLangGuai.swf");
         this.missionDic["mission27"].push("enemy/bossBaoZiJing.swf");
         this.missionDic["mission28"] = [];
         this.missionDic["mission28"].push("scene/sceneBaoTouShan.swf");
         this.missionDic["mission28"].push("scene/mcWhiteSpider.swf");
         this.missionDic["mission28"].push("scene/mcRedSpider.swf");
         this.missionDic["mission28"].push("scene/mcPurpleSpider.swf");
         this.missionDic["mission28"].push("enemy/enemyLvZhiZhu.swf");
         this.missionDic["mission28"].push("enemy/bossHuangShiJing.swf");
         this.missionDic["mission28"].push("enemy/enemyBaoXiaoYao.swf");
         this.missionDic["mission28"].push("enemy/enemyShiXiaoYao.swf");
         this.missionDic["mission29"] = [];
         this.missionDic["mission29"].push("scene/sceneZhuJieShan.swf");
         this.missionDic["mission29"].push("enemy/bossYuanSheng.swf");
         this.missionDic["mission29"].push("enemy/bossZhenYuanSheng.swf");
         this.missionDic["mission29"].push("enemy/enemyLanWuGong.swf");
         this.missionDic["mission29"].push("enemy/enemyPengXiaoYao-650.swf");
         this.missionDic["mission30"] = [];
         this.missionDic["mission30"].push("enemy/enemyBaoXiaoYao.swf");
         this.missionDic["mission30"].push("enemy/enemyXiNiuYao.swf");
         this.missionDic["mission30"].push("scene/sceneQinLongShan.swf");
         this.missionDic["mission30"].push("enemy/bossBiShuDaWang.swf");
         this.missionDic["mission30"].push("enemy/bossBiHanDaWang.swf");
         this.missionDic["mission30"].push("enemy/bossBiChenDaWang.swf");
         this.missionDic["mission31"] = [];
         this.missionDic["mission31"].push("scene/sceneBuJinChanSi.swf");
         this.missionDic["mission31"].push("enemy/enemyPengXiaoYao-650.swf");
         this.missionDic["mission31"].push("enemy/enemyTuYao.swf");
         this.missionDic["mission31"].push("enemy/bossYuTu.swf");
         this.missionDic["mission31"].push("enemy/bossZhenYuTu.swf");
         this.missionDic["mission32"] = [];
         this.missionDic["mission32"].push("scene/sceneLeiYinSi.swf");
         this.missionDic["mission32"].push("enemy/bossJiaYe.swf");
         this.missionDic["mission32"].push("enemy/bossNan.swf");
         this.missionDic["mission32"].push("enemy/enemyHeShang.swf");
         this.missionDic["mission32"].push("enemy/enemyXieWuSeng.swf");
         this.missionDic["mission32"].push("enemy/enemyXieFaSeng.swf");
         this.missionDic["mission33"] = [];
         this.missionDic["mission33"].push("scene/sceneYinShan.swf");
         this.missionDic["mission33"].push("enemy/bossTianGou.swf");
         this.missionDic["mission34"] = [];
         this.missionDic["mission34"].push("scene/sceneZhangEShan.swf");
         this.missionDic["mission34"].push("enemy/bossZhenZhen.swf");
         this.missionDic["mission35"] = [];
         this.missionDic["mission35"].push("scene/sceneYingShan.swf");
         this.missionDic["mission35"].push("enemy/bossFeiYi.swf");
         this.missionDic["mission36"] = [];
         this.missionDic["mission36"].push("scene/sceneNiuYangShan.swf");
         this.missionDic["mission36"].push("enemy/bossXuanGui.swf");
         this.missionDic["mission37"] = [];
         this.missionDic["mission37"].push("scene/sceneTuQiu.swf");
         this.missionDic["mission37"].push("enemy/bossYingLong.swf");
         this.missionDic["mission38"] = [];
         this.missionDic["mission38"].push("scene/sceneSanWeiShan.swf");
         this.missionDic["mission38"].push("enemy/bossQingNiao.swf");
         this.missionDic["mission39"] = [];
         this.missionDic["mission39"].push("scene/sceneZhangWeiShan.swf");
         this.missionDic["mission39"].push("enemy/bossZhuLong.swf");
         this.missionDic["mission40"] = [];
         this.missionDic["mission40"].push("scene/sceneMiLin.swf");
         this.missionDic["mission40"].push("enemy/bossCYZY.swf");
         this.missionDic["mission41"] = [];
         this.missionDic["mission41"].push("scene/sceneKunLun.swf");
         this.missionDic["mission41"].push("enemy/bossLuWu.swf");
         this.missionDic["mission42"] = [];
         this.missionDic["mission42"].push("scene/sceneYanHuoLin.swf");
         this.missionDic["mission42"].push("enemy/bossHuoDou.swf");
         this.missionDic["mission43"] = [];
         this.missionDic["mission43"].push("scene/sceneBeiYueShan.swf");
         this.missionDic["mission43"].push("enemy/bossZhuHuai.swf");
         this.missionDic["mission44"] = [];
         this.missionDic["mission44"].push("scene/sceneTaiShan.swf");
         this.missionDic["mission44"].push("enemy/bossZaoChi.swf");
         this.missionDic["tiangong"] = [];
         this.missionDic["tiangong"].push("scene/sceneXuZhang1.swf");
         this.missionDic["tiangong"].push("enemy/bossFoShou.swf");
         this.missionDic["tiangong"].push("enemy/enemyTianBing.swf");
         this._missionVec = new Vector.<BaseMissionVO>();
         this._missionVec.push(new BaseMissionVO(1,"新手章",1,new Point(480,400),this.missionDic["xinshou"]));
         this._missionVec.push(new BaseMissionVO(2,"花果山",2,new Point(150,780),this.missionDic["huaguoshan"]));
         this._missionVec.push(new BaseMissionVO(3,"五庄观",3,new Point(148,780),this.missionDic["wuzhuangguan"]));
         this._missionVec.push(new BaseMissionVO(4,"镇妖塔",4,new Point(100,375),[]));
         this._missionVec.push(new BaseMissionVO(5,"荆棘岭",5,new Point(100,375),this.missionDic["jingjiling"]));
         this._missionVec.push(new BaseMissionVO(6,"试炼场",6,new Point(100,330),this.missionDic["shilian"]));
         this._missionVec.push(new BaseMissionVO(7,"藏宝洞",7,new Point(100,330),this.missionDic["cangbaodong"]));
         this._missionVec.push(new BaseMissionVO(8,"竞技场",8,new Point(100,330),this.missionDic["jingjichang"]));
         this._missionVec.push(new BaseMissionVO(9,"黄泉路",9,new Point(800,390),this.missionDic["huangquanlu"]));
         this._missionVec.push(new BaseMissionVO(10,"赛太岁",10,new Point(204,390),this.missionDic["saitaisui"]));
         this._missionVec.push(new BaseMissionVO(11,"水帘洞",11,new Point(204,390),this.missionDic["shuiliandong"]));
         this._missionVec.push(new BaseMissionVO(12,"挑战七圣",12,new Point(204,390),this.missionDic["qisheng"]));
         this._missionVec.push(new BaseMissionVO(13,"龙宫",13,new Point(204,390),this.missionDic["longgong"]));
         this._missionVec.push(new BaseMissionVO(14,"水麒麟",14,new Point(204,390),this.missionDic["shuiqilin"]));
         this._missionVec.push(new BaseMissionVO(15,"火凤凰",15,new Point(204,390),this.missionDic["huofenghuang"]));
         this._missionVec.push(new BaseMissionVO(16,"迷宫",16,new Point(204,390),this.missionDic["migong"]));
         this._missionVec.push(new BaseMissionVO(17,"公会副本",17,new Point(204,390),this.missionDic["union"]));
         this._missionVec.push(new BaseMissionVO(18,"大雄宝殿",18,new Point(204,390),this.missionDic["daxiongbaodian"],1033,1032));
         this._missionVec.push(new BaseMissionVO(19,"玄武",19,new Point(204,390),this.missionDic["xuanwu"]));
         this._missionVec.push(new BaseMissionVO(20,"宰相府",20,new Point(204,390),this.missionDic["zaixiangfu"]));
         this._missionVec.push(new BaseMissionVO(21,"珊瑚礁",21,new Point(204,390),this.missionDic["shanhujiao"]));
         this._missionVec.push(new BaseMissionVO(22,"沉船",22,new Point(204,390),this.missionDic["chenchuan"]));
         this._missionVec.push(new BaseMissionVO(23,"深渊",23,new Point(204,390),this.missionDic["shenyuan"]));
         this._missionVec.push(new BaseMissionVO(100,"双叉岭",101,new Point(77,440),this.missionDic["mission1"],1001,0,"建议队伍：孙悟空<br>建议等级：1级<br>大妖五行：寅将军(木)<br>掉落物品：照妖镜图谱、虎筋<br>掉落紫装:<br>[孙悟空]3级行者棍"));
         this._missionVec.push(new BaseMissionVO(200,"盘蛇山",201,new Point(77,470),this.missionDic["mission2"],1002,1001,"建议队伍：孙悟空<br>大妖五行：小白龙[水]<br>掉落物品：1级强化石[普通难度];水之石[普通难度];绿玄晶[困难难度];紫晶、洗髓丹[噩梦难度]"));
         this._missionVec.push(new BaseMissionVO(300,"黑风洞",301,new Point(77,500),this.missionDic["mission3"],1003,1002,"建议队伍：孙悟空、小白龙<br>建议等级：3级<br>大妖五行：黑熊精[火]<br>掉落物品：熊筋<br>掉落紫装：<br>[孙悟空]5级棉衫、7级荧光"));
         this._missionVec.push(new BaseMissionVO(400,"高老庄",401,new Point(77,450),this.missionDic["mission4"],1004,1003,"建议队伍：孙悟空、小白龙<br>大妖五行：猪八戒[木]<br>掉落物品：彩玄果、木心果[普通难度];精霞石、莹月草[困难难度]4级洗练石[噩梦难度]"));
         this._missionVec.push(new BaseMissionVO(500,"黄风洞",501,new Point(70,340),this.missionDic["mission5"],1005,1004,"建议队伍：孙悟空、猪八戒<br>建议等级：5级<br>大妖五行：黄风怪[土]<br>掉落物品：一级强化石、玄金、貂鼠皮、驭兽袋图谱<br>草药洞掉落：止泻草、彼岸花、芫花<br>掉落紫装：<br>[猪八戒]3级狼牙拳套、5级布衫、7级荧光<br>[小白龙]3级青铜剑、5级麻布衣、7级荧光<br>[沙   僧]3级乌金铲"));
         this._missionVec.push(new BaseMissionVO(600,"流沙河",601,new Point(70,300),this.missionDic["mission6"],1006,1005,"建议队伍：孙悟空、猪八戒<br>大妖五行：沙僧[土]<br>掉落物品：石楠花[普通难度];邪雾花种子、月光草[困难难度]"));
         this._missionVec.push(new BaseMissionVO(700,"白虎岭",701,new Point(134,1020),this.missionDic["mission7"],1007,1006,"建议队伍：孙悟空、猪八戒<br>建议等级：7级<br>大妖五行：白骨精[土]<br>掉落物品：缚妖索图谱、一级强化石、玉骨、野果<br>掉落紫装：<br>[孙悟空]13级盘纹棍<br>[小白龙]13级青锋剑、7级荧光<br>[沙   僧]5级皮衣"));
         this._missionVec.push(new BaseMissionVO(800,"黑松林",801,new Point(60,480),this.missionDic["mission8"],1008,1007,"建议队伍：小白龙、沙僧<br>建议等级：9级<br>大妖五行：黄袍怪[火]<br>掉落物品：净瓶图谱、净瓶碎片、狼牙、狼毛、兽骨爪碎片<br>掉落紫装：<br>[孙悟空]15级兽纹衣<br>[小白龙]15级貂皮衣<br>[沙   僧]7级荧光"));
         this._missionVec.push(new BaseMissionVO(900,"平顶山",901,new Point(95,400),this.missionDic["mission9"],1009,1008,"建议队伍：孙悟空、猪八戒<br>建议等级：11级<br>大妖五行：金角大王[金]、银角大王[土]<br>掉落物品：狐尾、银角、兽骨盔碎片<br>草药洞掉落：柏果、落英花、乌舌兰<br>掉落紫装：<br>[孙悟空]17级风月<br>[猪八戒]13级龙牙拳套<br>[小白龙]17级风月"));
         this._missionVec.push(new BaseMissionVO(1000,"乌鸡国",1001,new Point(100,435),this.missionDic["mission10"],1010,1009,"建议队伍：猪八戒、小白龙<br>建议等级：13级<br>大妖五行：狮俐王[土]<br>关卡掉落物品：避水珠图谱、珍珠、兽骨甲碎片<br>掉落紫装：<br>[猪八戒]15级宵烛衣<br>[沙   僧]13级突云铲<br>水牢大妖五行：[水]<br>可能掉落：石玉、蚕丝、灯鱼内丹、蟾蜍内丹、寄居蟹内丹、宁神花种子、2级强化石"));
         this._missionVec.push(new BaseMissionVO(1100,"火云洞",1101,new Point(85,435),this.missionDic["mission11"],1011,1010,"建议队伍：小白龙、沙僧<br>建议等级：15级<br>大妖五行：红孩儿[火]<br>掉落物品：地火石、兽骨坠碎片<br>掉落紫装：<br>[孙悟空]23级阴阳棍、25级软猬衣、27级碧水<br>[猪八戒]17级风月"));
         this._missionVec.push(new BaseMissionVO(1200,"黑水河",1201,new Point(100,390),this.missionDic["mission12"],1012,1011,"建议队伍：孙悟空、沙僧<br>建议等级：17级<br>大妖五行：鼍龙[水]<br>掉落物品：镇妖塔图谱、镇妖塔碎片、鼍角、兽羽爪碎片<br>掉落紫装：<br>[小白龙]23级游龙剑、25级银环衣、27级碧水<br>[沙   僧]15级琥珀衣"));
         this._missionVec.push(new BaseMissionVO(1300,"车迟国",1301,new Point(100,390),this.missionDic["mission13"],1013,1012,"建议队伍：孙悟空、小龙女<br>建议等级：19级<br>大妖五行：虎力大仙[金]、鹿力大仙[木]、羊力大仙[火]、妖魂王[土]<br>掉落物品：兽羽盔碎片<br>掉落紫装：<br>[猪八戒]23级幻影拳套、25级紧身衣、27级碧水<br>[沙   僧]17级风月"));
         this._missionVec.push(new BaseMissionVO(1400,"通天河",1401,new Point(100,390),this.missionDic["mission14"],1014,1013,"建议队伍：孙悟空、猪八戒<br>建议等级：21级<br>大妖五行：灵感大王[土]<br>掉落物品：鱼鳍、兽羽甲碎片<br>掉落紫装：<br>[沙   僧]23级赤炎铲"));
         this._missionVec.push(new BaseMissionVO(1500,"金兜山",1501,new Point(100,390),this.missionDic["mission15"],1015,1014,"建议队伍：孙悟空、小龙女<br>建议等级：23级<br>大妖五行：独角兜[金]<br>掉落物品：玉镜、花褐玉、兽羽坠碎片、兽俐爪碎片<br>掉落紫装：<br>[沙   僧]25级乌蚕衣、27级碧水"));
         this._missionVec.push(new BaseMissionVO(1600,"琵琶洞",1601,new Point(100,390),this.missionDic["mission16"],1016,1015,"建议队伍：孙悟空、猪八戒<br>建议等级：25级<br>大妖五行：蝎子精[土]<br>掉落物品：赤铜、蝎尾、兽俐盔碎片<br>掉落紫装：<br>[孙悟空]33级云龙棍<br>[猪八戒]33级霹雳拳套<br>[小白龙]33级七星剑<br>木之镇碑掉落物品：土檀石、褐檀石、虹檀石、红岩晶"));
         this._missionVec.push(new BaseMissionVO(1700,"火焰山",1701,new Point(100,390),this.missionDic["mission17"],1017,1016,"建议队伍：小白龙、小龙女<br>建议等级：27级<br>大妖五行：铁扇公主[火]、牛魔王[金]<br>掉落物品：地月石、芭蕉叶、兽俐甲碎片<br>掉落紫装：<br>[沙   僧]33级梨花铲"));
         this._missionVec.push(new BaseMissionVO(1800,"乱石山",1801,new Point(100,390),this.missionDic["mission18"],1018,1017,"建议队伍：小龙女、沙僧<br>建议等级：29级<br>大妖五行：九头蛇[金]<br>掉落物品：兽俐坠碎片、金石<br>掉落紫装：<br>[孙悟空]35级战魂衣<br>[猪八戒]35级玄风衣"));
         this._missionVec.push(new BaseMissionVO(1900,"小雷音寺",1901,new Point(100,390),this.missionDic["mission19"],1019,1018,"建议队伍：小龙女、猪八戒<br>建议等级：31级<br>大妖五行：黄眉怪[金]<br>掉落物品：兽凝爪碎片、黄眼石<br>掉落紫装：<br>[小白龙]35级青莲衣<br>[沙僧]35级梵天衣"));
         this._missionVec.push(new BaseMissionVO(2000,"七绝山",2001,new Point(100,390),this.missionDic["mission20"],1020,1019,"建议队伍：猪八戒、小白龙（单人模式）<br>建议等级：33级<br>掉落物品：兽凝盔碎片、水蓝石<br>大妖五行：蟒蛇精[水]<br>掉落紫装：<br>[小白龙]37级卷云<br>[沙僧]37级卷云"));
         this._missionVec.push(new BaseMissionVO(2100,"麒麟山",2101,new Point(80,2225),this.missionDic["mission21"],1021,1020,"建议队伍：小白龙、小龙女<br>建议等级：35级<br>大妖五行：赛太岁[金]<br>掉落物品：兽凝甲碎片<br>掉落紫装：<br>[孙悟空]37级卷云<br>[猪八戒]37级卷云"));
         this._missionVec.push(new BaseMissionVO(2200,"盘丝洞",2201,new Point(100,390),this.missionDic["mission22"],1022,1021,"建议队伍：孙悟空、沙僧<br>建议等级：37级<br>大妖五行：蜘蛛精[水]<br>掉落物品：兽凝坠碎片<br>掉落紫装：<br>[猪八戒]43级雷光拳套<br>[小白龙]43级倚天剑"));
         this._missionVec.push(new BaseMissionVO(2300,"黄花观",2301,new Point(120,390),this.missionDic["mission23"],1023,1022,"建议队伍：孙悟空、猪八戒<br>建议等级：39级<br>大妖五行：蜈蚣精[土]<br>掉落紫装：<br>[孙悟空]43级天罡棍<br>[沙僧]43级飞鸿铲"));
         this._missionVec.push(new BaseMissionVO(2400,"狮驼岭",2401,new Point(120,390),this.missionDic["mission24"],1024,1023,"建议队伍：小白龙、沙僧<br>建议等级：41级<br>大妖五行：狮子精[火]、白象精[土]、大鹏精[水]<br>掉落紫装：<br>[小白龙]45级龙神衣"));
         this._missionVec.push(new BaseMissionVO(2500,"清华洞",2501,new Point(120,390),this.missionDic["mission25"],1025,1024,"建议队伍：孙悟空、沙僧<br>建议等级：43级<br>大妖五行：白鹿精[水]<br>掉落紫装：<br>[沙僧]45级佛谕衣"));
         this._missionVec.push(new BaseMissionVO(2600,"陷空山",2601,new Point(120,390),this.missionDic["mission26"],1026,1025,"建议队伍：孙悟空、猪八戒<br>建议等级：45级<br>大妖五行：地涌夫人[水]<br>掉落紫装：<br>[猪八戒]45级夜魔衣"));
         this._missionVec.push(new BaseMissionVO(2700,"隐雾山",2701,new Point(120,390),this.missionDic["mission27"],1027,1026,"建议队伍：小龙女、沙僧<br>建议等级：45级<br>大妖五行：豹子精[金]<br>掉落紫装：<br>[孙悟空]45级青龙衣"));
         this._missionVec.push(new BaseMissionVO(2800,"豹头山",2801,new Point(120,390),this.missionDic["mission28"],1028,1027,"建议队伍：孙悟空、猪八戒<br>建议等级：46级<br>大妖五行：黄狮精[土]<br>掉落紫装：<br>[小白龙]47级七彩"));
         this._missionVec.push(new BaseMissionVO(2900,"竹节山",2901,new Point(100,2255),this.missionDic["mission29"],1029,1028,"建议队伍：孙悟空、小龙女<br>建议等级：46级<br>大妖五行：九灵元圣[金]<br>掉落紫装：<br>[沙僧]47级七彩"));
         this._missionVec.push(new BaseMissionVO(3000,"青龙山",3001,new Point(120,390),this.missionDic["mission30"],1030,1029,"建议队伍：小白龙、猪八戒<br>建议等级：47级<br>大妖五行：辟暑大王[火]辟寒大王[水]辟尘大王[土]<br>掉落紫装：<br>[猪八戒]47级七彩"));
         this._missionVec.push(new BaseMissionVO(3100,"布金禅寺",3101,new Point(120,390),this.missionDic["mission31"],1031,1030,"建议队伍：孙悟空、猪八戒<br>建议等级：47级<br>大妖五行：玉兔精[水]<br>掉落紫装：<br>[孙悟空]47级七彩"));
         this._missionVec.push(new BaseMissionVO(3200,"雷音寺",3201,new Point(120,390),this.missionDic["mission32"],1032,1031,"建议队伍：小龙女、小白龙<br>建议等级：47级<br>大妖五行：阿傩[土]迦叶[金]<br>掉落：武僧卡、阿傩卡、迦叶卡"));
         this._missionVec.push(new BaseMissionVO(3300,"阴山",3301,new Point(120,390),this.missionDic["mission33"],1034,1033,"大妖五行：天狗[土]<br>掉落：白莹玉、软灵脂、仙狐延"));
         this._missionVec.push(new BaseMissionVO(3400,"章莪山",3401,new Point(120,390),this.missionDic["mission34"],1035,1034,"大妖五行：狰[火]<br>掉落：晶沙、火铜石、黄晶"));
         this._missionVec.push(new BaseMissionVO(3500,"英山",3501,new Point(120,390),this.missionDic["mission35"],1036,1035,"大妖五行：肥遗[水]<br>掉落：翠绿石、橄榄石"));
         this._missionVec.push(new BaseMissionVO(3600,"杻阳山",3601,new Point(120,390),this.missionDic["mission36"],1037,1036,"大妖五行：旋龟[水]<br>掉落：迷彩石、旋龟角"));
         this._missionVec.push(new BaseMissionVO(3700,"凶犁土丘",3701,new Point(120,390),this.missionDic["mission37"],1038,1037,"大妖五行：应龙[土]<br>掉落：金红石、水曲石、土裂晶、火络晶、橙砾石"));
         this._missionVec.push(new BaseMissionVO(3800,"三危山",3801,new Point(120,390),this.missionDic["mission38"],1039,1038,"大妖五行：青鸟[水]<br>掉落：金檀石、绿玛石"));
         this._missionVec.push(new BaseMissionVO(3900,"章尾山",3901,new Point(120,390),this.missionDic["mission39"],1040,1039,"大妖五行：烛龙[金]<br>掉落：玄紫石、玄黄石、蓝岚石、土青石"));
         this._missionVec.push(new BaseMissionVO(4000,"迷林",4001,new Point(120,390),this.missionDic["mission40"],1041,1040,"大妖五行：赤眼猪妖[火]<br>掉落：厉金石、水吟石、火流石"));
         this._missionVec.push(new BaseMissionVO(4100,"昆仑之丘",4101,new Point(120,390),this.missionDic["mission41"],1042,1041,"大妖五行：陆吾[水]<br>掉落：57级饰品(火)(土)"));
         this._missionVec.push(new BaseMissionVO(4200,"厌火林",4201,new Point(120,390),this.missionDic["mission42"],1043,1042,"大妖五行：祸斗[火]<br>掉落：57级饰品(木)(水)"));
         this._missionVec.push(new BaseMissionVO(4300,"北岳山",4301,new Point(120,390),this.missionDic["mission43"],1044,1043,"大妖五行：诸怀[土]<br>掉落：57级饰品(金)"));
         this._missionVec.push(new BaseMissionVO(4400,"太山",4401,new Point(120,390),this.missionDic["mission44"],1045,1044,"大妖五行：凿齿[土]<br>掉落：炼彩石"));
         this._missionVec.push(new BaseMissionVO(9999,"天宫",1,new Point(480,400),this.missionDic["tiangong"],1050,1033,"大闹天宫：挑战如来佛祖<br>掉落：天宫神器"));
         this._sceneVec = new Vector.<BaseSceneVO>();
         this._sceneVec.push(new BaseSceneVO(1,"MC_SCENE_XUZHANG_11","PIC_SCENE_BG_11",960,600));
         this._sceneVec.push(new BaseSceneVO(2,"MC_HUAGUOSHAN_LAYOUT_CLIP","",1440,900));
         this._sceneVec.push(new BaseSceneVO(3,"MC_WUZHUANGGUAN_LAYOUT_CLIP","PIC_SCENE_BG_31",1440,900));
         this._sceneVec.push(new BaseSceneVO(31,"MC_WUZHUANGGUAN_LAYOUT_CLIP2","PIC_SCENE_BG_31",2880,600));
         this._sceneVec.push(new BaseSceneVO(4,"MC_TOWER_LAYOUT_CLIP","PIC_SCENE_TOWER_BG",960,600));
         this._sceneVec.push(new BaseSceneVO(5,"MC_JINGJILING_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(51,"MC_JINGJILING_LAYOUT_CLIP2","",2880,600));
         this._sceneVec.push(new BaseSceneVO(6,"MC_SHILIAN_LAYOUT_CLIP","PIC_SHI_LIAN_BG",960,600));
         this._sceneVec.push(new BaseSceneVO(7,"MC_CANGBAODONG_LAYOUT_CLIP","",1440,900));
         this._sceneVec.push(new BaseSceneVO(8,"MC_LAYOUT_PK_SCENE_CLIP","",1440,900));
         this._sceneVec.push(new BaseSceneVO(9,"MC_HUANGQUANLU_LAYOUT_CLIP","PIC_HUANG_QUAN_LU_BG",2400,900));
         this._sceneVec.push(new BaseSceneVO(10,"MC_SCENE_LAYOUT_SAI_TAI_SUI_CLIP","PIC_SAI_TAI_SUI_BG_CLIP",960,600));
         this._sceneVec.push(new BaseSceneVO(11,"MC_SHUILIANDONG_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(12,"MC_QI_SHENG_LAYOUT_CLIP","PIC_QI_SHENG_BG",960,600));
         this._sceneVec.push(new BaseSceneVO(13,"MC_LONG_GONG_LAYOUT_CLIP","",3840,600));
         this._sceneVec.push(new BaseSceneVO(14,"MC_SCENE_LAYOUT_SHUI_QI_LIN_CLIP","PIC_SHUI_QI_LIN_CLIP",960,600));
         this._sceneVec.push(new BaseSceneVO(15,"MC_SCENE_LAYOUT_SHUI_QI_LIN_CLIP","PIC_SHUI_QI_LIN_CLIP",960,600));
         this._sceneVec.push(new BaseSceneVO(16,"MC_SCENE_LAYOUT_MI_GONG_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(17,"MC_UNION_BATTLE_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(18,"SCENE_DXBD_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(19,"MC_SCENE_LAYOUT_XUAN_WU_CLIP","PIC_FUBEN_XUAN_WU_CLIP",960,600));
         this._sceneVec.push(new BaseSceneVO(20,"SCENE_ZAI_XIANG_FU_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(21,"SCENE_SHAN_HU_JIAO_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(22,"SCENE_CHEN_CHUAN_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(23,"SCENE_SHEN_YUAN_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(101,"MC_SCENE_LAYOUT_CLIP_101","PIC_SCENE_BG_101",2400,600));
         this._sceneVec.push(new BaseSceneVO(201,"MC_SCENE_LAYOUT_CLIP_201","",1440,600));
         this._sceneVec.push(new BaseSceneVO(301,"MC_SCENE_LAYOUT_CLIP_301","PIC_SCENE_BG_301",1920,600));
         this._sceneVec.push(new BaseSceneVO(302,"MC_SCENE_LAYOUT_CLIP_302","PIC_SCENE_BG_302",960,600));
         this._sceneVec.push(new BaseSceneVO(401,"MC_SCENE_LAYOUT_CLIP_401","",1440,600));
         this._sceneVec.push(new BaseSceneVO(501,"MC_SCENE_LAYOUT_CLIP_501","PIC_SCENE_BG_501",1920,1200));
         this._sceneVec.push(new BaseSceneVO(502,"MC_SCENE_LAYOUT_CLIP_502","PIC_SCENE_BG_502",960,600));
         this._sceneVec.push(new BaseSceneVO(503,"MC_SCENE_LAYOUT_CLIP_503","",960,600));
         this._sceneVec.push(new BaseSceneVO(601,"MC_SCENE_LAYOUT_CLIP_601","",1440,600));
         this._sceneVec.push(new BaseSceneVO(701,"MC_SCENE_LAYOUT_CLIP_701","",2880,1200));
         this._sceneVec.push(new BaseSceneVO(702,"MC_SCENE_LAYOUT_CLIP_702","",1920,600));
         this._sceneVec.push(new BaseSceneVO(703,"MC_SCENE_LAYOUT_CLIP_703","",1920,600));
         this._sceneVec.push(new BaseSceneVO(704,"MC_SCENE_LAYOUT_CLIP_704","",1920,600));
         this._sceneVec.push(new BaseSceneVO(705,"MC_SCENE_LAYOUT_CLIP_705","",2880,600));
         this._sceneVec.push(new BaseSceneVO(801,"MC_SCENE_LAYOUT_CLIP_801","PIC_SCENE_BG_801",1440,600));
         this._sceneVec.push(new BaseSceneVO(802,"MC_SCENE_LAYOUT_CLIP_802","PIC_SCENE_BG_802",1440,600));
         this._sceneVec.push(new BaseSceneVO(803,"MC_SCENE_LAYOUT_CLIP_803","",1440,1200));
         this._sceneVec.push(new BaseSceneVO(804,"MC_SCENE_LAYOUT_CLIP_804","PIC_SCENE_BG_804",1440,600));
         this._sceneVec.push(new BaseSceneVO(805,"MC_SCENE_LAYOUT_CLIP_805","PIC_SCENE_BG_805",1920,600));
         this._sceneVec.push(new BaseSceneVO(806,"MC_SCENE_LAYOUT_CLIP_806","PIC_SCENE_BG_805",1920,600));
         this._sceneVec.push(new BaseSceneVO(807,"MC_SCENE_LAYOUT_CLIP_807","PIC_SCENE_BG_805",1920,600));
         this._sceneVec.push(new BaseSceneVO(808,"MC_SCENE_LAYOUT_CLIP_808","PIC_SCENE_BG_805",1920,600));
         this._sceneVec.push(new BaseSceneVO(809,"MC_SCENE_LAYOUT_CLIP_809","PIC_SCENE_BG_809",1440,600));
         this._sceneVec.push(new BaseSceneVO(901,"MC_SCENE_LAYOUT_CLIP_901","PIC_PING_DING_SHAN_BG",2190,600));
         this._sceneVec.push(new BaseSceneVO(902,"MC_SCENE_LAYOUT_CLIP_902","PIC_PING_DING_SHAN_BG",1160,1750));
         this._sceneVec.push(new BaseSceneVO(903,"MC_SCENE_LAYOUT_CLIP_903","PIC_SCENE_BG_903",960,600));
         this._sceneVec.push(new BaseSceneVO(904,"MC_SCENE_LAYOUT_CLIP_904","PIC_PING_DING_SHAN_BG",1440,600));
         this._sceneVec.push(new BaseSceneVO(1001,"MC_SCENE_LAYOUT_CLIP_1001","PIC_SCENE_BG_1001",2738,600));
         this._sceneVec.push(new BaseSceneVO(1002,"MC_SCENE_LAYOUT_CLIP_1002","",1920,600));
         this._sceneVec.push(new BaseSceneVO(1003,"MC_SCENE_LAYOUT_CLIP_1003","PIC_SCENE_BG_1003",1440,600));
         this._sceneVec.push(new BaseSceneVO(1004,"MC_SCENE_LAYOUT_CLIP_1004","PIC_SCENE_BG_1004",960,600));
         this._sceneVec.push(new BaseSceneVO(1101,"MC_SCENE_LAYOUT_CLIP_1101","PIC_SCENE_BG_1101",1920,600));
         this._sceneVec.push(new BaseSceneVO(1102,"MC_SCENE_LAYOUT_CLIP_1102","",1700,600));
         this._sceneVec.push(new BaseSceneVO(1103,"MC_SCENE_LAYOUT_CLIP_1103","",1920,600));
         this._sceneVec.push(new BaseSceneVO(1201,"MC_SCENE_LAYOUT_CLIP_1201","PIC_SCENE_BG_1201",2880,600));
         this._sceneVec.push(new BaseSceneVO(1202,"MC_SCENE_LAYOUT_CLIP_1202","",2880,600));
         this._sceneVec.push(new BaseSceneVO(1301,"MC_SCENE_LAYOUT_CLIP_1301","",3840,600));
         this._sceneVec.push(new BaseSceneVO(1302,"MC_SCENE_LAYOUT_CLIP_1302","PIC_SCENE_BG_1302",960,600));
         this._sceneVec.push(new BaseSceneVO(1401,"MC_SCENE_LAYOUT_CLIP_1401","PIC_SCENE_BG_1401",4800,600));
         this._sceneVec.push(new BaseSceneVO(1501,"MC_SCENE_LAYOUT_CLIP_1501","",4800,600));
         this._sceneVec.push(new BaseSceneVO(1601,"MC_SCENE_LAYOUT_CLIP_1601","PIC_SCENE_BG_1601",4800,600));
         this._sceneVec.push(new BaseSceneVO(1602,"MC_SCENE_LAYOUT_CLIP_1602","PIC_SCENE_BG_1602",960,600));
         this._sceneVec.push(new BaseSceneVO(1701,"MC_SCENE_LAYOUT_CLIP_1701","PIC_SCENE_BG_1701",1440,600));
         this._sceneVec.push(new BaseSceneVO(1702,"MC_SCENE_LAYOUT_CLIP_1702","",1920,600));
         this._sceneVec.push(new BaseSceneVO(1703,"MC_SCENE_LAYOUT_CLIP_1703","PIC_SCENE_BG_1703",960,600));
         this._sceneVec.push(new BaseSceneVO(1704,"MC_SCENE_LAYOUT_CLIP_1704","PIC_SCENE_BG_1704",960,600));
         this._sceneVec.push(new BaseSceneVO(1705,"MC_SCENE_LAYOUT_CLIP_1705","PIC_SCENE_BG_1705",960,600));
         this._sceneVec.push(new BaseSceneVO(1801,"MC_SCENE_LAYOUT_CLIP_1801","PIC_SCENE_BG_1801",3840,600));
         this._sceneVec.push(new BaseSceneVO(1901,"MC_SCENE_LAYOUT_CLIP_1901","",3840,600));
         this._sceneVec.push(new BaseSceneVO(2001,"MC_SCENE_LAYOUT_CLIP_2001","",4800,600));
         this._sceneVec.push(new BaseSceneVO(2101,"MC_SCENE_LAYOUT_CLIP_2101","",960,2400));
         this._sceneVec.push(new BaseSceneVO(2102,"MC_SCENE_LAYOUT_CLIP_2102","PIC_QI_LIN_SHAN_BG",960,600));
         this._sceneVec.push(new BaseSceneVO(2201,"MC_SCENE_LAYOUT_CLIP_2201","PIC_PAN_SI_DONG_BG",4800,600));
         this._sceneVec.push(new BaseSceneVO(2301,"MC_SCENE_LAYOUT_CLIP_2301","",4800,600));
         this._sceneVec.push(new BaseSceneVO(2401,"MC_SCENE_LAYOUT_CLIP_2401","",4800,600));
         this._sceneVec.push(new BaseSceneVO(2402,"MC_SCENE_LAYOUT_CLIP_2402","",960,600));
         this._sceneVec.push(new BaseSceneVO(2501,"MC_SCENE_LAYOUT_CLIP_2501","PIC_QING_HUA_DONG_BG",4800,600));
         this._sceneVec.push(new BaseSceneVO(2601,"MC_SCENE_LAYOUT_CLIP_2601","PIC_XIAN_KONG_SHAN_BG",3840,600));
         this._sceneVec.push(new BaseSceneVO(2701,"MC_SCENE_LAYOUT_CLIP_2701","PIC_YIN_WU_SHAN_BG",3840,600));
         this._sceneVec.push(new BaseSceneVO(2801,"MC_SCENE_LAYOUT_CLIP_2801","",3840,600));
         this._sceneVec.push(new BaseSceneVO(2901,"MC_SCENE_LAYOUT_CLIP_2901","",960,2400));
         this._sceneVec.push(new BaseSceneVO(2902,"MC_SCENE_LAYOUT_CLIP_2902","PIC_ZHU_JIE_SHAN_BG",960,600));
         this._sceneVec.push(new BaseSceneVO(3001,"MC_SCENE_LAYOUT_CLIP_3001","",3840,600));
         this._sceneVec.push(new BaseSceneVO(3101,"MC_SCENE_LAYOUT_CLIP_3101","",3840,600));
         this._sceneVec.push(new BaseSceneVO(3201,"MC_SCENE_LAYOUT_CLIP_3201","",4800,600));
         this._sceneVec.push(new BaseSceneVO(3301,"SCENE_YIN_SHAN_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(3401,"SCENE_ZHANG_E_SHAN_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(3501,"SCENE_YING_SHAN_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(3601,"SCENE_NIU_YANG_SHAN_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(3701,"SCENE_TU_QIU_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(3801,"SCENE_SAN_WEI_SHAN_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(3901,"SCENE_ZHANG_WEI_SHAN_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(4001,"SCENE_MI_LIN_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(4101,"SCENE_KUN_LUN_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(4201,"SCENE_YAN_HUO_LIN_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(4301,"SCENE_BEI_YUE_SHAN_LAYOUT_CLIP","",1440,600));
         this._sceneVec.push(new BaseSceneVO(4401,"SCENE_TAI_SHAN_LAYOUT_CLIP","",1440,600));
      }
      
      public function findMissionVO(param1:int) : BaseMissionVO
      {
         var _loc2_:BaseMissionVO = null;
         for each(_loc2_ in this._missionVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findSceneVO(param1:int) : BaseSceneVO
      {
         var _loc2_:BaseSceneVO = null;
         for each(_loc2_ in this._sceneVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

