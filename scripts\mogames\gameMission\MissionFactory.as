package mogames.gameMission
{
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.baihuling.BaiHuLingMission;
   import mogames.gameMission.mission.baihuling.scene.SceneBaiHuLing01;
   import mogames.gameMission.mission.baihuling.scene.SceneBaiHuLing02;
   import mogames.gameMission.mission.baihuling.scene.SceneBaiHuLing03;
   import mogames.gameMission.mission.baihuling.scene.SceneBaiHuLing04;
   import mogames.gameMission.mission.baihuling.scene.SceneBaiHuLing05;
   import mogames.gameMission.mission.baotoushan.SceneBaoTouShan;
   import mogames.gameMission.mission.beiyueshan.SceneBeiYueShan;
   import mogames.gameMission.mission.bujinchansi.SceneBuJinChanSi;
   import mogames.gameMission.mission.cangbaodong.SceneCangBaoDong;
   import mogames.gameMission.mission.chechiguo.SceneCheChiGuo01;
   import mogames.gameMission.mission.chechiguo.SceneCheChiGuo02;
   import mogames.gameMission.mission.chenchuan.SceneChenChuan;
   import mogames.gameMission.mission.daxiongbaodian.SceneDXBD;
   import mogames.gameMission.mission.gaolaozhuang.SceneGaoLaoZhuang;
   import mogames.gameMission.mission.heifengdong.SceneHeiFengDong01;
   import mogames.gameMission.mission.heifengdong.SceneHeiFengDong02;
   import mogames.gameMission.mission.heishuihe.SceneHeiShuiHe01;
   import mogames.gameMission.mission.heishuihe.SceneHeiShuiHe02;
   import mogames.gameMission.mission.heisonglin.SceneHeiSongLin01;
   import mogames.gameMission.mission.heisonglin.SceneHeiSongLin02;
   import mogames.gameMission.mission.heisonglin.SceneHeiSongLin03;
   import mogames.gameMission.mission.heisonglin.SceneHeiSongLin04;
   import mogames.gameMission.mission.heisonglin.SceneHeiSongLin05;
   import mogames.gameMission.mission.heisonglin.SceneHeiSongLin06;
   import mogames.gameMission.mission.heisonglin.SceneHeiSongLin07;
   import mogames.gameMission.mission.heisonglin.SceneHeiSongLin08;
   import mogames.gameMission.mission.heisonglin.SceneHeiSongLin09;
   import mogames.gameMission.mission.huangfengdong.HuangFengDongMission;
   import mogames.gameMission.mission.huangfengdong.scene.SceneHuangFengDong01;
   import mogames.gameMission.mission.huangfengdong.scene.SceneHuangFengDong02;
   import mogames.gameMission.mission.huangfengdong.scene.SceneHuangFengDong03;
   import mogames.gameMission.mission.huanghuaguan.SceneHuangHuaGuan;
   import mogames.gameMission.mission.huangquanlu.SceneHuangQuanLu;
   import mogames.gameMission.mission.huofenghuang.SceneFengHuang;
   import mogames.gameMission.mission.huoyanshan.SceneHuoYanShan01;
   import mogames.gameMission.mission.huoyanshan.SceneHuoYanShan02;
   import mogames.gameMission.mission.huoyanshan.SceneHuoYanShan03;
   import mogames.gameMission.mission.huoyanshan.SceneHuoYanShan04;
   import mogames.gameMission.mission.huoyanshan.SceneHuoYanShan05;
   import mogames.gameMission.mission.huoyundong.SceneHuoYunDong1101;
   import mogames.gameMission.mission.huoyundong.SceneHuoYunDong1102;
   import mogames.gameMission.mission.huoyundong.SceneHuoYunDong1103;
   import mogames.gameMission.mission.jindoushan.SceneJinDouShan;
   import mogames.gameMission.mission.jingjichang.co.JingJiChangMission;
   import mogames.gameMission.mission.jingjichang.co.SceneJingJiChang;
   import mogames.gameMission.mission.kunlun.SceneKunLun;
   import mogames.gameMission.mission.leiyinsi.SceneLeiYinSi;
   import mogames.gameMission.mission.liushahe.SceneLiuShaHe;
   import mogames.gameMission.mission.longgong.SceneLongGong;
   import mogames.gameMission.mission.luanshishan.SceneLuanShiShan;
   import mogames.gameMission.mission.migong.SceneMiGong;
   import mogames.gameMission.mission.milin.SceneMiLin;
   import mogames.gameMission.mission.niuyangshan.SceneNiuYangShan;
   import mogames.gameMission.mission.npc.huaguoshan.HuaGuoShanMission;
   import mogames.gameMission.mission.npc.huaguoshan.SceneHuaGuoShan;
   import mogames.gameMission.mission.npc.huaguoshan.SceneShuiLianDong;
   import mogames.gameMission.mission.npc.jingjiling.JinJiLingMission;
   import mogames.gameMission.mission.npc.jingjiling.SceneJingJiLing;
   import mogames.gameMission.mission.npc.jingjiling.SceneZhuLin;
   import mogames.gameMission.mission.npc.wuzhuangguan.SceneHouYuan;
   import mogames.gameMission.mission.npc.wuzhuangguan.SceneWuZhuangGuan;
   import mogames.gameMission.mission.npc.wuzhuangguan.WuZhuangGuanMission;
   import mogames.gameMission.mission.pansheshan.ScenePSS;
   import mogames.gameMission.mission.pansidong.ScenePanSiDong01;
   import mogames.gameMission.mission.pindingshan.PinDingShanMission;
   import mogames.gameMission.mission.pindingshan.scene.ScenePingDingShan01;
   import mogames.gameMission.mission.pindingshan.scene.ScenePingDingShan02;
   import mogames.gameMission.mission.pindingshan.scene.ScenePingDingShan03;
   import mogames.gameMission.mission.pindingshan.scene.ScenePingDingShan04;
   import mogames.gameMission.mission.pipadong.PiPaDongMission;
   import mogames.gameMission.mission.pipadong.ScenePiPaDong;
   import mogames.gameMission.mission.pipadong.SceneZhenBei;
   import mogames.gameMission.mission.qijueshan.SceneQiJueShan;
   import mogames.gameMission.mission.qilinshan.QiLinShanMission;
   import mogames.gameMission.mission.qilinshan.SceneQiLinShan01;
   import mogames.gameMission.mission.qilinshan.SceneQiLinShan02;
   import mogames.gameMission.mission.qinghuadong.SceneQingHuaDong;
   import mogames.gameMission.mission.qinniushan.SceneQinNiuShan;
   import mogames.gameMission.mission.qisheng.SceneQiSheng;
   import mogames.gameMission.mission.saitaisui.SceneSaiTaiSui;
   import mogames.gameMission.mission.sanweishan.SceneSanWeiShan;
   import mogames.gameMission.mission.shanhujiao.SceneShanHuJiao;
   import mogames.gameMission.mission.shenyuan.SceneShenYuan;
   import mogames.gameMission.mission.shilianchang.SceneShiLianChang;
   import mogames.gameMission.mission.shituoling.SceneShiTuoLing01;
   import mogames.gameMission.mission.shituoling.SceneShiTuoLing02;
   import mogames.gameMission.mission.shuangchaling.SceneSCL;
   import mogames.gameMission.mission.shuiqilin.SceneShuiQiLin;
   import mogames.gameMission.mission.taishan.SceneTaiShan;
   import mogames.gameMission.mission.tongtianhe.SceneTongTianHe;
   import mogames.gameMission.mission.tuqiu.SceneTuQiu;
   import mogames.gameMission.mission.union.UnionScene;
   import mogames.gameMission.mission.wujiguo.WuJiGuoMission;
   import mogames.gameMission.mission.wujiguo.scene.SceneShuiLao;
   import mogames.gameMission.mission.wujiguo.scene.SceneWuJiGuo1001;
   import mogames.gameMission.mission.wujiguo.scene.SceneWuJiGuo1002;
   import mogames.gameMission.mission.wujiguo.scene.SceneWuJiGuo1003;
   import mogames.gameMission.mission.xiakongshan.SceneXianKongShan;
   import mogames.gameMission.mission.xiaoleiyin.SceneXiaoLeiYin;
   import mogames.gameMission.mission.xuanwu.SceneXuanWu;
   import mogames.gameMission.mission.xuzhang.XuZhangMission;
   import mogames.gameMission.mission.xuzhang.scene.XuZhangScene0;
   import mogames.gameMission.mission.yanhuolin.SceneYanHuoLin;
   import mogames.gameMission.mission.yingshan.SceneYingShan;
   import mogames.gameMission.mission.yinshan.SceneYinShan;
   import mogames.gameMission.mission.yinwushan.SceneYinWuShan;
   import mogames.gameMission.mission.zaixiangfu.SceneZaiXiangFu;
   import mogames.gameMission.mission.zhangeshan.SceneZhangEShan;
   import mogames.gameMission.mission.zhangweishan.SceneZhangWeiShan;
   import mogames.gameMission.mission.zhenyaota.SceneZhenYaoTa;
   import mogames.gameMission.mission.zhenyaota.ZhenYaoTaMission;
   import mogames.gameMission.mission.tiangong.TianGongMission;
   import mogames.gameMission.mission.tiangong.SceneTianGong;
   import mogames.gameMission.mission.zhujieshan.SceneZhuJieShan01;
   import mogames.gameMission.mission.zhujieshan.SceneZhuJieShan02;
   import mogames.gameMission.mission.zhujieshan.ZhuJieShanMission;
   
   public class MissionFactory
   {
      private static var _instance:MissionFactory;
      
      public function MissionFactory()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : MissionFactory
      {
         if(!_instance)
         {
            _instance = new MissionFactory();
         }
         return _instance;
      }
      
      public function newMission(param1:int) : BaseMission
      {
         switch(param1)
         {
            case 1:
               return new XuZhangMission();
            case 2:
               return new HuaGuoShanMission();
            case 3:
               return new WuZhuangGuanMission();
            case 4:
               return new ZhenYaoTaMission();
            case 5:
               return new JinJiLingMission();
            case 6:
               return new BaseMission(false);
            case 8:
               return new JingJiChangMission();
            case 500:
               return new HuangFengDongMission();
            case 700:
               return new BaiHuLingMission();
            case 900:
               return new PinDingShanMission();
            case 1000:
               return new WuJiGuoMission();
            case 1600:
               return new PiPaDongMission();
            case 2100:
               return new QiLinShanMission();
            case 2900:
               return new ZhuJieShanMission();
            case 9999:
               return new TianGongMission();
            default:
               return new BaseMission();
         }
      }
      
      public function newScene(param1:int, param2:BaseSceneVO, param3:BaseMission) : BaseScene
      {
         switch(param1)
         {
            case 1:
               return new XuZhangScene0(param2,param3);
            case 2:
               return new SceneHuaGuoShan(param2,param3);
            case 3:
               return new SceneWuZhuangGuan(param2,param3);
            case 31:
               return new SceneHouYuan(param2,param3);
            case 4:
               return new SceneZhenYaoTa(param2,param3);
            case 5:
               return new SceneJingJiLing(param2,param3);
            case 51:
               return new SceneZhuLin(param2,param3);
            case 6:
               return new SceneShiLianChang(param2,param3);
            case 7:
               return new SceneCangBaoDong(param2,param3);
            case 8:
               return new SceneJingJiChang(param2,param3);
            case 9:
               return new SceneHuangQuanLu(param2,param3);
            case 10:
               return new SceneSaiTaiSui(param2,param3);
            case 11:
               return new SceneShuiLianDong(param2,param3);
            case 12:
               return new SceneQiSheng(param2,param3);
            case 13:
               return new SceneLongGong(param2,param3);
            case 14:
               return new SceneShuiQiLin(param2,param3);
            case 15:
               return new SceneFengHuang(param2,param3);
            case 16:
               return new SceneMiGong(param2,param3);
            case 17:
               return new UnionScene(param2,param3);
            case 18:
               return new SceneDXBD(param2,param3);
            case 19:
               return new SceneXuanWu(param2,param3);
            case 20:
               return new SceneZaiXiangFu(param2,param3);
            case 21:
               return new SceneShanHuJiao(param2,param3);
            case 22:
               return new SceneChenChuan(param2,param3);
            case 23:
               return new SceneShenYuan(param2,param3);
            case 101:
               return new SceneSCL(param2,param3);
            case 201:
               return new ScenePSS(param2,param3);
            case 301:
               return new SceneHeiFengDong01(param2,param3);
            case 302:
               return new SceneHeiFengDong02(param2,param3);
            case 401:
               return new SceneGaoLaoZhuang(param2,param3);
            case 501:
               return new SceneHuangFengDong01(param2,param3);
            case 502:
               return new SceneHuangFengDong02(param2,param3);
            case 503:
               return new SceneHuangFengDong03(param2,param3);
            case 601:
               return new SceneLiuShaHe(param2,param3);
            case 701:
               return new SceneBaiHuLing01(param2,param3);
            case 702:
               return new SceneBaiHuLing02(param2,param3);
            case 703:
               return new SceneBaiHuLing03(param2,param3);
            case 704:
               return new SceneBaiHuLing04(param2,param3);
            case 705:
               return new SceneBaiHuLing05(param2,param3);
            case 801:
               return new SceneHeiSongLin01(param2,param3);
            case 802:
               return new SceneHeiSongLin02(param2,param3);
            case 803:
               return new SceneHeiSongLin03(param2,param3);
            case 804:
               return new SceneHeiSongLin04(param2,param3);
            case 805:
               return new SceneHeiSongLin05(param2,param3);
            case 806:
               return new SceneHeiSongLin06(param2,param3);
            case 807:
               return new SceneHeiSongLin07(param2,param3);
            case 808:
               return new SceneHeiSongLin08(param2,param3);
            case 809:
               return new SceneHeiSongLin09(param2,param3);
            case 901:
               return new ScenePingDingShan01(param2,param3);
            case 902:
               return new ScenePingDingShan02(param2,param3);
            case 903:
               return new ScenePingDingShan03(param2,param3);
            case 904:
               return new ScenePingDingShan04(param2,param3);
            case 1001:
               return new SceneWuJiGuo1001(param2,param3);
            case 1002:
               return new SceneWuJiGuo1002(param2,param3);
            case 1003:
               return new SceneWuJiGuo1003(param2,param3);
            case 1004:
               return new SceneShuiLao(param2,param3);
            case 1101:
               return new SceneHuoYunDong1101(param2,param3);
            case 1102:
               return new SceneHuoYunDong1102(param2,param3);
            case 1103:
               return new SceneHuoYunDong1103(param2,param3);
            case 1201:
               return new SceneHeiShuiHe01(param2,param3);
            case 1202:
               return new SceneHeiShuiHe02(param2,param3);
            case 1301:
               return new SceneCheChiGuo01(param2,param3);
            case 1302:
               return new SceneCheChiGuo02(param2,param3);
            case 1401:
               return new SceneTongTianHe(param2,param3);
            case 1501:
               return new SceneJinDouShan(param2,param3);
            case 1601:
               return new ScenePiPaDong(param2,param3);
            case 1602:
               return new SceneZhenBei(param2,param3);
            case 1701:
               return new SceneHuoYanShan01(param2,param3);
            case 1702:
               return new SceneHuoYanShan02(param2,param3);
            case 1703:
               return new SceneHuoYanShan03(param2,param3);
            case 1704:
               return new SceneHuoYanShan04(param2,param3);
            case 1705:
               return new SceneHuoYanShan05(param2,param3);
            case 1801:
               return new SceneLuanShiShan(param2,param3);
            case 1901:
               return new SceneXiaoLeiYin(param2,param3);
            case 2001:
               return new SceneQiJueShan(param2,param3);
            case 2101:
               return new SceneQiLinShan01(param2,param3);
            case 2102:
               return new SceneQiLinShan02(param2,param3);
            case 2201:
               return new ScenePanSiDong01(param2,param3);
            case 2301:
               return new SceneHuangHuaGuan(param2,param3);
            case 2401:
               return new SceneShiTuoLing01(param2,param3);
            case 2402:
               return new SceneShiTuoLing02(param2,param3);
            case 2501:
               return new SceneQingHuaDong(param2,param3);
            case 2601:
               return new SceneXianKongShan(param2,param3);
            case 2701:
               return new SceneYinWuShan(param2,param3);
            case 2801:
               return new SceneBaoTouShan(param2,param3);
            case 2901:
               return new SceneZhuJieShan01(param2,param3);
            case 2902:
               return new SceneZhuJieShan02(param2,param3);
            case 3001:
               return new SceneQinNiuShan(param2,param3);
            case 3101:
               return new SceneBuJinChanSi(param2,param3);
            case 3201:
               return new SceneLeiYinSi(param2,param3);
            case 3301:
               return new SceneYinShan(param2,param3);
            case 3401:
               return new SceneZhangEShan(param2,param3);
            case 3501:
               return new SceneYingShan(param2,param3);
            case 3601:
               return new SceneNiuYangShan(param2,param3);
            case 3701:
               return new SceneTuQiu(param2,param3);
            case 3801:
               return new SceneSanWeiShan(param2,param3);
            case 3901:
               return new SceneZhangWeiShan(param2,param3);
            case 4001:
               return new SceneMiLin(param2,param3);
            case 4101:
               return new SceneKunLun(param2,param3);
            case 4201:
               return new SceneYanHuoLin(param2,param3);
            case 4301:
               return new SceneBeiYueShan(param2,param3);
            case 4401:
               return new SceneTaiShan(param2,param3);
            case 9999:
               return new SceneTianGong(param2,param3);
            default:
               return new BaseScene(param2,param3);
         }
      }
   }
}

