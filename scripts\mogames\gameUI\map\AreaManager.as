package mogames.gameUI.map
{
   import mogames.gameEffect.EffectManager;
   import mogames.gamePKG.GameChecker;
   import mogames.gamePKG.GameProxy;
   import mogames.gameUI.map.clip.BaseMap;
   import mogames.gameUI.map.clip.MapFiveClip;
   import mogames.gameUI.map.clip.MapFourClip;
   import mogames.gameUI.map.clip.MapLGClip;
   import mogames.gameUI.map.clip.MapOneClip;
   import mogames.gameUI.map.clip.MapThreeClip;
   import mogames.gameUI.map.clip.MapTianGongClip;
   import mogames.gameUI.map.clip.MapTwoClip;
   import mogames.gameUI.map.clip.MapWorldClip;
   import mogames.gameUI.tip.TipManager;
   
   public class AreaManager
   {
      private static var _instance:AreaManager;
      
      public static var mapID:int = 1;
      
      private var _map:BaseMap;
      
      public function AreaManager()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : AreaManager
      {
         if(!_instance)
         {
            _instance = new AreaManager();
         }
         return _instance;
      }
      
      public static function clean() : void
      {
         MapHeroTopModule.clean();
         MapTopMenuModule.clean();
         MapMenuModule.clean();
         TipManager.instance().removeTip();
         if(!_instance)
         {
            return;
         }
         _instance.destroy();
         _instance = null;
      }
      
      public function showMap() : void
      {
         MapHeroTopModule.instance().init();
         MapMenuModule.instance().init();
         MapTopMenuModule.instance().init();
         this.changeMap(mapID);
         GameProxy.instance().submitData();
      }
      
      public function changeMap(param1:int) : void
      {
         this.destroy();
         EffectManager.instance().addTransEffect();
         if(param1 <= 0)
         {
            param1 = 1;
         }
         mapID = param1;
         switch(mapID)
         {
            case 1:
               this._map = new MapOneClip();
               break;
            case 2:
               this._map = new MapTwoClip();
               break;
            case 3:
               this._map = new MapThreeClip();
               break;
            case 4:
               this._map = new MapFourClip();
               break;
            case 5:
               this._map = new MapFiveClip();
               break;
            case 100:
               this._map = new MapWorldClip();
               break;
            case 200:
               this._map = new MapLGClip();
               break;
            case 300:
               this._map = new MapTianGongClip();
               break;
         }
         MapMenuModule.instance().setWordVisible(param1 != 100);
         new GameChecker().checkCheat();
      }
      
      public function showFirst(param1:int) : void
      {
         mapID = param1;
         this.showMap();
      }
      
      private function destroy() : void
      {
         if(!this._map)
         {
            return;
         }
         this._map.destroy();
         this._map = null;
      }
   }
}

