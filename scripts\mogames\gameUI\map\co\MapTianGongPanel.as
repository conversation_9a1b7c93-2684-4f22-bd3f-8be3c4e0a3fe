package mogames.gameUI.map.co
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import mogames.AssetManager;
   import mogames.event.UIEvent;
   import mogames.gameData.base.Sint;
   import mogames.gameObj.display.BaseSprite;
   import mogames.gameUI.tip.TipManager;
   import utils.MethodUtil;
   
   public class MapTianGongPanel extends BaseSprite
   {
      private var ui:MovieClip;
      
      public var missions:Array;
      
      public function MapTianGongPanel()
      {
         super();
         this.ui = AssetManager.newMCRes("UI_AREA_TIANGONG_MAP");
         addChild(this.ui);
         
         // 添加天宫特色按钮事件监听
         this.ui.btnNanTianMen.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.ui.btnLingXiaoBaoDian.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.ui.btnPanTaoYuan.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.ui.btnTianBing.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         
         // 配置天宫任务点
         this.missions = [];
         this.missions.push({
            "mc":this.ui.mcNanTianMen,
            "mID":new Sint(3000)  // 南天门任务ID
         });
         this.missions.push({
            "mc":this.ui.mcLingXiaoBaoDian,
            "mID":new Sint(3100)  // 凌霄宝殿任务ID
         });
         this.missions.push({
            "mc":this.ui.mcPanTaoYuan,
            "mID":new Sint(3200)  // 蟠桃园任务ID
         });
         this.missions.push({
            "mc":this.ui.mcTianBingYingZhai,
            "mID":new Sint(3300)  // 天兵营寨任务ID
         });
         this.missions.push({
            "mc":this.ui.mcTaiShangLaoJun,
            "mID":new Sint(3400)  // 太上老君任务ID
         });
         
         this.initMissions();
      }
      
      private function initMissions() : void
      {
         var _loc1_:Object = null;
         for each(_loc1_ in this.missions)
         {
            _loc1_.mc.addEventListener(MouseEvent.MOUSE_OVER,this.onMissionOver,false,0,true);
            _loc1_.mc.addEventListener(MouseEvent.MOUSE_OUT,this.onMissionOut,false,0,true);
            _loc1_.mc.addEventListener(MouseEvent.CLICK,this.onMissionClick,false,0,true);
            _loc1_.mc.buttonMode = true;
         }
      }
      
      private function onMissionOver(param1:MouseEvent) : void
      {
         var _loc2_:Object = this.findMissionByMC(param1.currentTarget);
         if(_loc2_)
         {
            dispatchEvent(new UIEvent(UIEvent.MAP_EVENT,{
               "type":"onMissionTip",
               "data":_loc2_.mID
            }));
         }
      }
      
      private function onMissionOut(param1:MouseEvent) : void
      {
         TipManager.instance().removeTip();
      }
      
      private function onMissionClick(param1:MouseEvent) : void
      {
         var _loc2_:Object = this.findMissionByMC(param1.currentTarget);
         if(_loc2_)
         {
            dispatchEvent(new UIEvent(UIEvent.MAP_EVENT,{
               "type":"onMission",
               "data":_loc2_.mID
            }));
         }
      }
      
      private function findMissionByMC(param1:Object) : Object
      {
         var _loc2_:Object = null;
         for each(_loc2_ in this.missions)
         {
            if(_loc2_.mc == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      private function onDown(param1:MouseEvent) : void
      {
         var _loc2_:String = param1.currentTarget.name;
         dispatchEvent(new UIEvent(UIEvent.MAP_EVENT,{
            "type":_loc2_,
            "data":null
         }));
      }
      
      // 显示天宫特色提示
      public function showTipTianBing(param1:Boolean) : void
      {
         this.ui.mcTipTianBing.visible = param1;
         if(param1)
         {
            this.ui.mcTipTianBing.play();
         }
         else
         {
            this.ui.mcTipTianBing.stop();
         }
      }
      
      public function showTipPanTao(param1:Boolean) : void
      {
         this.ui.mcTipPanTao.visible = param1;
         if(param1)
         {
            this.ui.mcTipPanTao.play();
         }
         else
         {
            this.ui.mcTipPanTao.stop();
         }
      }
      
      override public function destroy() : void
      {
         var _loc1_:Object = null;
         
         // 移除按钮事件监听
         this.ui.btnNanTianMen.removeEventListener(MouseEvent.CLICK,this.onDown);
         this.ui.btnLingXiaoBaoDian.removeEventListener(MouseEvent.CLICK,this.onDown);
         this.ui.btnPanTaoYuan.removeEventListener(MouseEvent.CLICK,this.onDown);
         this.ui.btnTianBing.removeEventListener(MouseEvent.CLICK,this.onDown);
         
         // 移除任务点事件监听
         for each(_loc1_ in this.missions)
         {
            _loc1_.mc.removeEventListener(MouseEvent.MOUSE_OVER,this.onMissionOver);
            _loc1_.mc.removeEventListener(MouseEvent.MOUSE_OUT,this.onMissionOut);
            _loc1_.mc.removeEventListener(MouseEvent.CLICK,this.onMissionClick);
         }
         
         this.missions = null;
         super.destroy();
      }
   }
}
